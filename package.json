{"name": "jerry-joo-website", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "backend:dev": "nodemon src/app.js", "backend:start": "node src/app.js", "backend:prod": "NODE_ENV=production node src/app.js", "backend:test": "node src/test-backend.js", "test:security": "node scripts/security-test.js", "test:oauth": "echo 'Testing OAuth configuration...' && node -e \"console.log('Google Client ID:', process.env.REACT_APP_GOOGLE_CLIENT_ID || 'Not configured - will use demo mode')\""}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@hookform/resolvers": "^3.9.0", "@paypal/checkout-server-sdk": "^1.0.3", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-oauth/google": "^0.12.2", "@tanstack/react-query": "^5.56.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.8.0", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-rate-limit": "^8.0.0", "express-session": "^1.18.1", "express-slow-down": "^2.0.3", "express-validator": "^7.2.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "get-video-duration": "^4.1.0", "glob": "^11.0.3", "google-auth-library": "^10.1.0", "helmet": "^8.1.0", "input-otp": "^1.2.4", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "mime-types": "^3.0.1", "moment": "^2.30.1", "mongoose": "^8.16.3", "mongoose-aggregate-paginate-v2": "^1.1.4", "mongoose-paginate-v2": "^1.9.1", "morgan": "^1.10.0", "mp3-duration": "^1.1.0", "mpesa-node-sdk": "^1.0.0", "multer": "^2.0.2", "music-metadata": "^11.6.1", "next-themes": "^0.3.0", "node-cron": "^4.2.1", "node-ffprobe": "^3.0.0", "node-watch": "^0.7.4", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "paypal-rest-sdk": "^1.8.1", "pdfkit": "^0.17.1", "pesapal-node": "^1.0.1", "progress-stream": "^2.0.0", "pump": "^3.0.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "rimraf": "^6.0.1", "sanitize-filename": "^1.6.3", "sharp": "^0.34.3", "sonner": "^1.5.0", "stripe": "^18.3.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "through2": "^4.0.2", "uuid": "^11.1.0", "validator": "^13.15.15", "vaul": "^0.9.3", "winston": "^3.17.0", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@modelcontextprotocol/server-sequential-thinking": "^2025.7.1", "@playwright/mcp": "^0.0.29", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "nodemon": "^3.1.10", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}