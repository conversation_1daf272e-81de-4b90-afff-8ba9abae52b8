const crypto = require('crypto');
const validator = require('validator');

/**
 * Security Service
 * Centralizes security patterns and suspicious activity detection
 * Consolidates duplicate security logic from multiple files
 */
class SecurityService {
  constructor() {
    // Security thresholds and configuration
    this.config = {
      maxLoginAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      suspiciousActivityThreshold: 10,
      rateLimitWindow: 15 * 60 * 1000, // 15 minutes
      maxRequestsPerWindow: 100,
      passwordMinLength: 8,
      passwordRequireSpecialChars: true
    };

    // In-memory stores (in production, use Redis)
    this.loginAttempts = new Map();
    this.suspiciousActivity = new Map();
    this.rateLimitStore = new Map();
    this.blockedIPs = new Set();
  }

  // ============================================================================
  // SUSPICIOUS ACTIVITY DETECTION
  // ============================================================================

  /**
   * Detect suspicious activity patterns
   * Consolidates logic from securityMiddleware.js, securityMonitoringService.js, DigitalRights.js
   */
  detectSuspiciousActivity(req, activityType = 'general') {
    try {
      const ip = this.getClientIP(req);
      const userAgent = req.headers['user-agent'] || '';
      const timestamp = Date.now();

      // Get or create activity record for this IP
      if (!this.suspiciousActivity.has(ip)) {
        this.suspiciousActivity.set(ip, {
          attempts: 0,
          lastAttempt: timestamp,
          activities: [],
          blocked: false
        });
      }

      const record = this.suspiciousActivity.get(ip);

      // Add current activity
      record.activities.push({
        type: activityType,
        timestamp,
        userAgent,
        path: req.path,
        method: req.method
      });

      // Keep only recent activities (last hour)
      const oneHourAgo = timestamp - (60 * 60 * 1000);
      record.activities = record.activities.filter(a => a.timestamp > oneHourAgo);

      // Check for suspicious patterns
      const suspiciousScore = this.calculateSuspiciousScore(record, req);

      if (suspiciousScore >= this.config.suspiciousActivityThreshold) {
        this.blockIP(ip, 'Suspicious activity detected');
        return {
          suspicious: true,
          score: suspiciousScore,
          blocked: true,
          reason: 'Multiple suspicious activities detected'
        };
      }

      return {
        suspicious: suspiciousScore > 5,
        score: suspiciousScore,
        blocked: false
      };

    } catch (error) {
      console.error('Suspicious activity detection error:', error);
      return { suspicious: false, score: 0, blocked: false };
    }
  }

  /**
   * Calculate suspicious activity score
   */
  calculateSuspiciousScore(record, req) {
    let score = 0;
    const recentActivities = record.activities;

    // Multiple rapid requests
    const rapidRequests = recentActivities.filter(a => 
      Date.now() - a.timestamp < 60000 // Last minute
    ).length;
    if (rapidRequests > 20) score += 3;

    // Multiple failed login attempts
    const failedLogins = recentActivities.filter(a => 
      a.type === 'failed_login'
    ).length;
    if (failedLogins > 3) score += 4;

    // Suspicious user agent patterns
    const userAgent = req.headers['user-agent'] || '';
    if (this.isSuspiciousUserAgent(userAgent)) score += 2;

    // Multiple different endpoints accessed rapidly
    const uniquePaths = new Set(recentActivities.map(a => a.path));
    if (uniquePaths.size > 10 && recentActivities.length > 15) score += 2;

    // SQL injection or XSS attempts in query parameters
    const queryString = req.url.split('?')[1] || '';
    if (this.containsMaliciousPatterns(queryString)) score += 5;

    return score;
  }

  /**
   * Check for suspicious user agent patterns
   */
  isSuspiciousUserAgent(userAgent) {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /scanner/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Check for malicious patterns in input
   */
  containsMaliciousPatterns(input) {
    const maliciousPatterns = [
      /(\bselect\b|\bunion\b|\binsert\b|\bdelete\b|\bdrop\b|\bupdate\b).*(\bfrom\b|\binto\b|\bwhere\b)/i,
      /<script[^>]*>.*?<\/script>/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /\.\.\//,
      /\/etc\/passwd/i,
      /\/proc\/self\/environ/i
    ];

    return maliciousPatterns.some(pattern => pattern.test(input));
  }

  // ============================================================================
  // RATE LIMITING
  // ============================================================================

  /**
   * Check rate limit for IP address
   */
  checkRateLimit(req) {
    try {
      const ip = this.getClientIP(req);
      const now = Date.now();
      const windowStart = now - this.config.rateLimitWindow;

      // Get or create rate limit record
      if (!this.rateLimitStore.has(ip)) {
        this.rateLimitStore.set(ip, []);
      }

      const requests = this.rateLimitStore.get(ip);

      // Remove old requests outside the window
      const recentRequests = requests.filter(timestamp => timestamp > windowStart);
      this.rateLimitStore.set(ip, recentRequests);

      // Check if limit exceeded
      if (recentRequests.length >= this.config.maxRequestsPerWindow) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: windowStart + this.config.rateLimitWindow
        };
      }

      // Add current request
      recentRequests.push(now);

      return {
        allowed: true,
        remaining: this.config.maxRequestsPerWindow - recentRequests.length,
        resetTime: windowStart + this.config.rateLimitWindow
      };

    } catch (error) {
      console.error('Rate limit check error:', error);
      return { allowed: true, remaining: 100, resetTime: Date.now() };
    }
  }

  // ============================================================================
  // LOGIN ATTEMPT TRACKING
  // ============================================================================

  /**
   * Track failed login attempts
   */
  trackFailedLogin(identifier) {
    try {
      const now = Date.now();

      if (!this.loginAttempts.has(identifier)) {
        this.loginAttempts.set(identifier, {
          attempts: 0,
          lastAttempt: now,
          lockedUntil: null
        });
      }

      const record = this.loginAttempts.get(identifier);
      record.attempts += 1;
      record.lastAttempt = now;

      // Lock account if too many attempts
      if (record.attempts >= this.config.maxLoginAttempts) {
        record.lockedUntil = now + this.config.lockoutDuration;
        return {
          locked: true,
          attemptsRemaining: 0,
          lockedUntil: record.lockedUntil
        };
      }

      return {
        locked: false,
        attemptsRemaining: this.config.maxLoginAttempts - record.attempts,
        lockedUntil: null
      };

    } catch (error) {
      console.error('Failed login tracking error:', error);
      return { locked: false, attemptsRemaining: 5, lockedUntil: null };
    }
  }

  /**
   * Check if account is locked
   */
  isAccountLocked(identifier) {
    try {
      const record = this.loginAttempts.get(identifier);
      
      if (!record || !record.lockedUntil) {
        return { locked: false };
      }

      const now = Date.now();
      
      if (now > record.lockedUntil) {
        // Lock has expired, reset attempts
        record.attempts = 0;
        record.lockedUntil = null;
        return { locked: false };
      }

      return {
        locked: true,
        lockedUntil: record.lockedUntil,
        remainingTime: record.lockedUntil - now
      };

    } catch (error) {
      console.error('Account lock check error:', error);
      return { locked: false };
    }
  }

  /**
   * Reset login attempts for successful login
   */
  resetLoginAttempts(identifier) {
    try {
      this.loginAttempts.delete(identifier);
    } catch (error) {
      console.error('Reset login attempts error:', error);
    }
  }

  // ============================================================================
  // IP BLOCKING
  // ============================================================================

  /**
   * Block IP address
   */
  blockIP(ip, reason = 'Security violation') {
    try {
      this.blockedIPs.add(ip);
      console.log(`🚫 IP blocked: ${ip} - Reason: ${reason}`);
      
      // In production, you'd also update firewall rules or database
    } catch (error) {
      console.error('IP blocking error:', error);
    }
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ip) {
    return this.blockedIPs.has(ip);
  }

  /**
   * Unblock IP address
   */
  unblockIP(ip) {
    try {
      this.blockedIPs.delete(ip);
      console.log(`✅ IP unblocked: ${ip}`);
    } catch (error) {
      console.error('IP unblocking error:', error);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get client IP address
   */
  getClientIP(req) {
    return req.ip || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           '0.0.0.0';
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const errors = [];

    if (password.length < this.config.passwordMinLength) {
      errors.push(`Password must be at least ${this.config.passwordMinLength} characters long`);
    }

    if (this.config.passwordRequireSpecialChars) {
      if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      if (!/[0-9]/.test(password)) {
        errors.push('Password must contain at least one number');
      }
      if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('Password must contain at least one special character');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Sanitize input to prevent XSS
   */
  sanitizeInput(input) {
    if (typeof input !== 'string') {
      return input;
    }

    return validator.escape(input);
  }

  /**
   * Get security statistics
   */
  getSecurityStats() {
    return {
      blockedIPs: this.blockedIPs.size,
      activeLoginAttempts: this.loginAttempts.size,
      suspiciousActivities: this.suspiciousActivity.size,
      rateLimitEntries: this.rateLimitStore.size
    };
  }
}

// Export singleton instance
module.exports = new SecurityService();
