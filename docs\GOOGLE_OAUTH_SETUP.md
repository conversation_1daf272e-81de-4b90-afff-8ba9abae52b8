# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the Jerry Joo website.

## 🔧 Google Cloud Console Setup

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `jerry-joo-website`
4. Click "Create"

### Step 2: Enable APIs

1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for and enable these APIs:
   - **Google+ API** (for user profile information)
   - **Google Identity Services** (for OAuth)

### Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen first:
   - Choose "External" user type
   - Fill in required fields:
     - App name: `Jerry Joo Music`
     - User support email: `<EMAIL>`
     - Developer contact: `<EMAIL>`
   - Add scopes: `email`, `profile`, `openid`
   - Add test users if needed

4. Create OAuth 2.0 Client ID:
   - Application type: "Web application"
   - Name: `Jerry <PERSON> Website`
   - Authorized JavaScript origins:
     - `http://localhost:3000` (for development)
     - `https://yourdomain.com` (for production)
   - Authorized redirect URIs:
     - `http://localhost:3000` (for development)
     - `https://yourdomain.com` (for production)

5. Copy the **Client ID** (it will look like: `123456789-abcdef.apps.googleusercontent.com`)

## 🔑 Environment Configuration

### Step 1: Create Environment Files

Create a `.env` file in your project root:

```bash
# Google OAuth Configuration
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com

# Backend Configuration
JWT_SECRET=your-secure-jwt-secret-here
SESSION_SECRET=your-secure-session-secret-here
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-bcrypt-hashed-password

# Database
MONGODB_URI=mongodb://localhost:27017/jerryjoo_music

# Security
NODE_ENV=production
CSRF_SECRET=your-csrf-secret-here
```

### Step 2: Update Production Environment

For production deployment, set these environment variables:

```bash
# Vercel/Netlify
REACT_APP_GOOGLE_CLIENT_ID=your-production-client-id.apps.googleusercontent.com

# Heroku/Railway/DigitalOcean
GOOGLE_CLIENT_ID=your-production-client-id.apps.googleusercontent.com
```

## 🧪 Testing the Implementation

### Development Mode

If you haven't set up Google OAuth yet, the application will run in **DEMO MODE**:

1. Click "Continue with Google (Demo)"
2. Select from demo users:
   - Admin User (<EMAIL>)
   - Premium User (<EMAIL>)
   - Regular User (<EMAIL>)

### Production Mode

With proper Google OAuth setup:

1. Click "Continue with Google"
2. Google OAuth popup will appear
3. User selects Google account
4. Application receives verified user data
5. User is logged in with proper authentication

## 🔒 Security Features

### Token Verification

The backend verifies Google tokens using `google-auth-library`:

```javascript
const { OAuth2Client } = require('google-auth-library');
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Verify token
const ticket = await client.verifyIdToken({
  idToken: googleToken,
  audience: process.env.GOOGLE_CLIENT_ID
});
```

### Session Management

- JWT tokens with 4-hour expiration
- Secure session regeneration on login
- CSRF protection for all forms
- Rate limiting on authentication endpoints

### User Data Handling

- Minimal data collection (email, name, profile picture)
- Secure token storage
- No sensitive data in localStorage
- Proper logout and session cleanup

## 🚀 Deployment Checklist

### Before Deployment

- [ ] Google Cloud project created
- [ ] OAuth 2.0 credentials configured
- [ ] Production domains added to authorized origins
- [ ] Environment variables set
- [ ] SSL certificate configured (required for production OAuth)

### After Deployment

- [ ] Test Google login flow
- [ ] Verify token validation
- [ ] Check session management
- [ ] Test logout functionality
- [ ] Monitor authentication logs

## 🐛 Troubleshooting

### Common Issues

#### "Invalid Client ID" Error
- Check that `REACT_APP_GOOGLE_CLIENT_ID` matches your Google Cloud Console client ID
- Ensure the client ID ends with `.apps.googleusercontent.com`
- Verify the domain is added to authorized JavaScript origins

#### "Redirect URI Mismatch" Error
- Add your domain to authorized redirect URIs in Google Cloud Console
- Ensure exact match including protocol (http/https) and port

#### "Access Blocked" Error
- Complete OAuth consent screen configuration
- Add test users if app is not published
- Verify app domain ownership

#### Demo Mode Not Working
- Check that `REACT_APP_GOOGLE_CLIENT_ID` is not set or contains "your-google-client-id"
- Ensure `NODE_ENV` is set to "development"

### Debug Mode

Enable debug logging by adding to your `.env`:

```bash
DEBUG=google-auth:*
```

## 📞 Support

For additional help:

1. Check [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
2. Review [Google Identity Services](https://developers.google.com/identity/gsi/web)
3. Contact support: `<EMAIL>`

---

**Last Updated**: 2025-01-19  
**Version**: 1.0  
**Status**: Production Ready
