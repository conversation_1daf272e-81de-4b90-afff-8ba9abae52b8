const axios = require('axios');
require('dotenv').config();

/**
 * Backend Authentication Test Suite
 * Tests the unified authentication system and verifies JWT fixes
 */

const BASE_URL = 'http://localhost:5000';
const TEST_ADMIN = {
  username: 'jerryjoo_admin',
  password: 'Jerry<PERSON>oo2024!Admin'
};

let authToken = null;

/**
 * Test utilities
 */
function logTest(testName, status, message = '') {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏳';
  console.log(`${statusIcon} ${testName}: ${message}`);
}

function logSection(sectionName) {
  console.log('\n' + '='.repeat(60));
  console.log(`🧪 ${sectionName}`);
  console.log('='.repeat(60));
}

/**
 * Test server health
 */
async function testServerHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/api/health`);
    
    if (response.status === 200 && response.data.status === 'OK') {
      logTest('Server Health Check', 'PASS', 'Server is running');
      return true;
    } else {
      logTest('Server Health Check', 'FAIL', 'Server health check failed');
      return false;
    }
  } catch (error) {
    logTest('Server Health Check', 'FAIL', `Server not accessible: ${error.message}`);
    return false;
  }
}

/**
 * Test admin login via /myballs/login
 */
async function testMyballsLogin() {
  try {
    const response = await axios.post(`${BASE_URL}/myballs/login`, TEST_ADMIN);
    
    if (response.status === 200 && response.data.success && response.data.token) {
      authToken = response.data.token;
      logTest('Myballs Login', 'PASS', 'Admin login successful');
      
      // Verify token structure
      const tokenParts = authToken.split('.');
      if (tokenParts.length === 3) {
        logTest('JWT Token Structure', 'PASS', 'Token has correct format');
      } else {
        logTest('JWT Token Structure', 'FAIL', 'Invalid token format');
      }
      
      return true;
    } else {
      logTest('Myballs Login', 'FAIL', 'Login failed or no token received');
      return false;
    }
  } catch (error) {
    logTest('Myballs Login', 'FAIL', `Login error: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

/**
 * Test API admin login
 */
async function testAPILogin() {
  try {
    const response = await axios.post(`${BASE_URL}/api/admin/auth/login`, TEST_ADMIN);
    
    if (response.status === 200 && response.data.success && response.data.data.token) {
      const apiToken = response.data.data.token;
      logTest('API Admin Login', 'PASS', 'API login successful');
      
      // Compare token format with myballs token
      if (authToken && apiToken) {
        // Both should have same structure (3 parts)
        const myballsParts = authToken.split('.');
        const apiParts = apiToken.split('.');
        
        if (myballsParts.length === apiParts.length) {
          logTest('Token Format Consistency', 'PASS', 'Both endpoints generate consistent token format');
        } else {
          logTest('Token Format Consistency', 'FAIL', 'Token formats differ between endpoints');
        }
      }
      
      return true;
    } else {
      logTest('API Admin Login', 'FAIL', 'API login failed');
      return false;
    }
  } catch (error) {
    logTest('API Admin Login', 'FAIL', `API login error: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

/**
 * Test protected endpoint access
 */
async function testProtectedEndpoint() {
  if (!authToken) {
    logTest('Protected Endpoint Access', 'FAIL', 'No auth token available');
    return false;
  }

  try {
    const response = await axios.get(`${BASE_URL}/api/admin/dashboard/stats`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (response.status === 200 && response.data.success) {
      logTest('Protected Endpoint Access', 'PASS', 'Dashboard stats accessible with token');
      
      // Verify user data in response
      if (response.data.data.user && response.data.data.user.username === TEST_ADMIN.username) {
        logTest('Token User Data', 'PASS', 'Correct user data in response');
      } else {
        logTest('Token User Data', 'FAIL', 'Incorrect or missing user data');
      }
      
      return true;
    } else {
      logTest('Protected Endpoint Access', 'FAIL', 'Dashboard access failed');
      return false;
    }
  } catch (error) {
    logTest('Protected Endpoint Access', 'FAIL', `Access error: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

/**
 * Test token validation endpoint
 */
async function testTokenValidation() {
  if (!authToken) {
    logTest('Token Validation', 'FAIL', 'No auth token available');
    return false;
  }

  try {
    const response = await axios.post(`${BASE_URL}/api/admin/auth/validate`, {
      token: authToken
    });
    
    if (response.status === 200 && response.data.valid) {
      logTest('Token Validation', 'PASS', 'Token validation successful');
      return true;
    } else {
      logTest('Token Validation', 'FAIL', 'Token validation failed');
      return false;
    }
  } catch (error) {
    logTest('Token Validation', 'FAIL', `Validation error: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

/**
 * Test authentication status endpoint
 */
async function testAuthStatus() {
  if (!authToken) {
    logTest('Auth Status Check', 'FAIL', 'No auth token available');
    return false;
  }

  try {
    const response = await axios.get(`${BASE_URL}/api/admin/auth/status`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (response.status === 200 && response.data.authenticated) {
      logTest('Auth Status Check', 'PASS', 'Authentication status correct');
      return true;
    } else {
      logTest('Auth Status Check', 'FAIL', 'Authentication status incorrect');
      return false;
    }
  } catch (error) {
    logTest('Auth Status Check', 'FAIL', `Status check error: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

/**
 * Test invalid credentials
 */
async function testInvalidCredentials() {
  try {
    const response = await axios.post(`${BASE_URL}/myballs/login`, {
      username: 'invalid_user',
      password: 'invalid_password'
    });
    
    // Should not reach here
    logTest('Invalid Credentials Test', 'FAIL', 'Invalid credentials were accepted');
    return false;
  } catch (error) {
    if (error.response?.status === 401) {
      logTest('Invalid Credentials Test', 'PASS', 'Invalid credentials properly rejected');
      return true;
    } else {
      logTest('Invalid Credentials Test', 'FAIL', `Unexpected error: ${error.message}`);
      return false;
    }
  }
}

/**
 * Test unauthorized access
 */
async function testUnauthorizedAccess() {
  try {
    const response = await axios.get(`${BASE_URL}/api/admin/dashboard/stats`);
    
    // Should not reach here
    logTest('Unauthorized Access Test', 'FAIL', 'Unauthorized access was allowed');
    return false;
  } catch (error) {
    if (error.response?.status === 401) {
      logTest('Unauthorized Access Test', 'PASS', 'Unauthorized access properly blocked');
      return true;
    } else {
      logTest('Unauthorized Access Test', 'FAIL', `Unexpected error: ${error.message}`);
      return false;
    }
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🎵 Jerry Joo Backend Authentication Test Suite');
  console.log('Testing unified JWT authentication system...\n');

  const results = {
    total: 0,
    passed: 0,
    failed: 0
  };

  // Test server health
  logSection('Server Health');
  const serverHealthy = await testServerHealth();
  results.total++;
  if (serverHealthy) results.passed++; else results.failed++;

  if (!serverHealthy) {
    console.log('\n❌ Server is not running. Please start the backend server first.');
    console.log('Run: npm run backend:dev');
    return;
  }

  // Test authentication
  logSection('Authentication Tests');
  
  const myballsLogin = await testMyballsLogin();
  results.total++;
  if (myballsLogin) results.passed++; else results.failed++;

  const apiLogin = await testAPILogin();
  results.total++;
  if (apiLogin) results.passed++; else results.failed++;

  // Test protected endpoints
  logSection('Protected Endpoint Tests');
  
  const protectedAccess = await testProtectedEndpoint();
  results.total++;
  if (protectedAccess) results.passed++; else results.failed++;

  const tokenValidation = await testTokenValidation();
  results.total++;
  if (tokenValidation) results.passed++; else results.failed++;

  const authStatus = await testAuthStatus();
  results.total++;
  if (authStatus) results.passed++; else results.failed++;

  // Test security
  logSection('Security Tests');
  
  const invalidCreds = await testInvalidCredentials();
  results.total++;
  if (invalidCreds) results.passed++; else results.failed++;

  const unauthorizedAccess = await testUnauthorizedAccess();
  results.total++;
  if (unauthorizedAccess) results.passed++; else results.failed++;

  // Results summary
  logSection('Test Results Summary');
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);

  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Authentication system is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the authentication implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test suite error:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
