# Jerry Joo Music Website - Backend Implementation

## 🎵 Overview

This is a comprehensive backend implementation for the Jerry Joo music website's admin dashboard, featuring secure file upload, media management, payment processing, and automated scheduling systems.

## ✨ Features

### 🔐 Admin Authentication & Access Control
- **Separate admin authentication** system (distinct from client-side users)
- **Role-based access control** with granular permissions
- **JWT-based authentication** with secure session management
- **Account lockout protection** after failed login attempts
- **Password hashing** with bcrypt (12 rounds)

### 📁 File Upload & Management
- **Multi-file upload** support (audio, video, artwork)
- **Automatic file organization** into structured directories
- **File validation** for supported formats
- **Metadata extraction** for audio/video files
- **Cross-reference system** for related files

### 🗂️ Organized File Storage Structure
```
TRACKS/
├── MUSIC/
│   ├── SINGLES/     # Individual audio tracks
│   └── ALBUMS/      # Album audio collections
├── VIDEOS/
│   ├── SINGLES/     # Individual video files
│   └── ALBUMS/      # Album video collections
└── ARTWORK/
    ├── SINGLES/     # Single track artwork
    └── ALBUMS/      # Album artwork
```

### ⏰ Scheduling System
- **Future release scheduling** for content
- **Automated publishing** via cron jobs
- **Manual publish override** for admins
- **Release date management**

### 🔗 Cross-Reference System
- **Automatic file association** based on base names
- **Related content linking** (audio ↔ video)
- **Efficient database queries** for related files
- **Smart content discovery**

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- MongoDB 4.4+
- FFmpeg (for video processing)

### Installation

1. **Clone and install dependencies:**
```bash
cd JERRYJOO-WEBSITE
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start MongoDB:**
```bash
# Using MongoDB service
sudo systemctl start mongod

# Or using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

4. **Start the backend server:**
```bash
# Development mode (with auto-reload)
npm run backend:dev

# Production mode
npm run backend:start
```

### Default Admin Account
- **Username:** `jerryjoo_admin`
- **Password:** `JerryJoo2024!Admin`
- **Email:** `<EMAIL>`

⚠️ **Change the default password immediately in production!**

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication Endpoints

#### Admin Login
```http
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "jerryjoo_admin",
  "password": "JerryJoo2024!Admin"
}
```

#### Admin Logout
```http
POST /api/admin/auth/logout
Authorization: Bearer <token>
```

### File Management Endpoints

#### Upload Files
```http
POST /api/admin/files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- files: [File] (multiple files supported)
- contentType: "single" | "album"
- title: string
- artist: string
- genre: string[] (optional)
- description: string (optional)
- releaseDate: ISO date string
- albumId: string (optional, for album tracks)
```

#### Get Tracks
```http
GET /api/admin/files/tracks?page=1&limit=20&contentType=single&status=published
Authorization: Bearer <token>
```

#### Get Single Track
```http
GET /api/admin/files/tracks/:id
Authorization: Bearer <token>
```

#### Update Track
```http
PUT /api/admin/files/tracks/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Title",
  "artist": "Updated Artist",
  "description": "Updated description",
  "releaseDate": "2024-12-25T00:00:00.000Z"
}
```

#### Delete Track
```http
DELETE /api/admin/files/tracks/:id
Authorization: Bearer <token>
```

### Scheduling Endpoints

#### Get Scheduled Releases
```http
GET /api/admin/schedule/releases
Authorization: Bearer <token>
```

#### Manually Publish Track
```http
POST /api/admin/schedule/publish/:id
Authorization: Bearer <token>
```

### Dashboard Endpoints

#### Get Dashboard Stats
```http
GET /api/admin/dashboard/stats
Authorization: Bearer <token>
```

## 🏗️ Architecture

### Database Models

#### Admin Model
- User management with roles and permissions
- Account security features
- Activity logging

#### Track Model
- File information (audio, video, artwork)
- Metadata storage
- Cross-reference system
- Scheduling information

#### Album Model
- Collection management
- Track relationships
- Album-level metadata

### Services

#### File Upload Service
- Multer configuration for file handling
- Metadata extraction (music-metadata, ffprobe)
- File organization and validation
- Error handling and cleanup

#### Authentication Service
- JWT token management
- Password security
- Session handling
- Permission checking

### Scheduled Jobs

#### Content Publishing (Every minute)
- Checks for scheduled content ready to publish
- Automatically publishes when release date is reached
- Logs all publishing activities

#### File Cleanup (Daily at 2 AM)
- Removes temporary files older than 24 hours
- Maintains storage efficiency

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Server
NODE_ENV=development
PORT=5000

# Database
MONGODB_URI=mongodb://localhost:27017/jerryjoo_music

# Security
ADMIN_JWT_SECRET=your_secure_secret_here
SESSION_SECRET=your_session_secret_here

# File Storage
TRACKS_BASE_PATH=./TRACKS

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_...
PAYPAL_CLIENT_ID=...
PESAPAL_CONSUMER_KEY=...
```

### Supported File Formats

#### Audio Files
- `.mp3` (up to 100MB)
- `.wav` (up to 100MB)
- `.flac` (up to 100MB)
- `.m4a` (up to 100MB)

#### Video Files
- `.mp4` (up to 500MB)
- `.mov` (up to 500MB)
- `.avi` (up to 500MB)
- `.mkv` (up to 500MB)

#### Image Files (Artwork)
- `.jpg` / `.jpeg` (up to 10MB)
- `.png` (up to 10MB)
- `.webp` (up to 10MB)

## 🛡️ Security Features

### Authentication Security
- JWT tokens with expiration
- Secure password hashing (bcrypt)
- Account lockout after failed attempts
- Session management with MongoDB store

### File Upload Security
- File type validation
- Size limits enforcement
- Secure file naming
- Path traversal prevention

### API Security
- Rate limiting
- CORS configuration
- Helmet security headers
- Input validation and sanitization

## 📊 Monitoring & Logging

### Winston Logging
- Structured logging with multiple levels
- File-based log storage
- Admin action tracking
- Security event logging

### Health Monitoring
- Database connection status
- System uptime tracking
- Error rate monitoring

## 🚀 Deployment

### Production Checklist

1. **Environment Setup:**
   - Set `NODE_ENV=production`
   - Configure secure secrets
   - Set up SSL certificates

2. **Database:**
   - Use MongoDB Atlas or dedicated server
   - Configure replica sets for high availability
   - Set up automated backups

3. **File Storage:**
   - Consider cloud storage (AWS S3, Cloudinary)
   - Set up CDN for file delivery
   - Configure backup strategies

4. **Security:**
   - Change default admin credentials
   - Configure firewall rules
   - Set up monitoring and alerts

### Docker Deployment (Optional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src ./src
EXPOSE 5000
CMD ["npm", "run", "backend:prod"]
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include logging for important operations
4. Write tests for new features
5. Update documentation

## 📝 License

This project is proprietary software for Jerry Joo Music Website.

---

**🎵 Built with ❤️ for Jerry Joo Music**
