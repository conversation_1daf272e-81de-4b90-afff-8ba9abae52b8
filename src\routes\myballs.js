const express = require('express');
const path = require('path');
const authService = require('../services/authService');
const { auditLog } = require('../middleware/adminAuth');

const router = express.Router();

/**
 * Admin Login Routes (/myballs)
 * File-system based admin authentication with fixed JWT token generation
 * Resolves audience/issuer mismatches and standardizes token format
 */

// ============================================================================
// ADMIN LOGIN PAGE
// ============================================================================

/**
 * Serve admin login page
 */
router.get('/', (req, res) => {
  try {
    // Simple HTML login page
    const loginPage = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jerry Joo Admin Panel</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1b69 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #ff4444;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .logo p {
            color: #ccc;
            font-size: 1.1em;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #ddd;
            font-weight: 500;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #ff4444;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
        }
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
        }
        .error {
            background: rgba(255, 68, 68, 0.2);
            color: #ff6b6b;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 68, 68, 0.3);
        }
        .success {
            background: rgba(68, 255, 68, 0.2);
            color: #6bff6b;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(68, 255, 68, 0.3);
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🎵 JERRY JOO</h1>
            <p>Admin Panel</p>
        </div>
        
        <div id="message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn">Login to Admin Panel</button>
        </form>
        
        <div class="footer">
            <p>Jerry Joo Music Website Admin</p>
            <p>Secure Authentication System</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            
            try {
                const response = await fetch('/myballs/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    messageDiv.innerHTML = '<div class="success">Login successful! Redirecting...</div>';
                    
                    // Store token
                    localStorage.setItem('adminToken', data.token);
                    
                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = '/api/admin/dashboard/stats';
                    }, 1000);
                } else {
                    messageDiv.innerHTML = '<div class="error">' + data.message + '</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="error">Login failed. Please try again.</div>';
                console.error('Login error:', error);
            }
        });
    </script>
</body>
</html>`;

    res.send(loginPage);
  } catch (error) {
    console.error('Error serving login page:', error);
    res.status(500).send('Internal server error');
  }
});

// ============================================================================
// ADMIN LOGIN ENDPOINT
// ============================================================================

/**
 * Process admin login with standardized JWT token generation
 * Fixes JWT secret mismatches and audience/issuer inconsistencies
 */
router.post('/login', auditLog('admin_login'), async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required',
        code: 'MISSING_CREDENTIALS'
      });
    }

    // Authenticate admin using unified AuthService
    const admin = await authService.authenticateAdmin(username, password);

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Generate standardized JWT token (fixes audience/issuer mismatches)
    const token = authService.generateToken(admin);

    // Generate refresh token for future use
    const refreshToken = authService.generateRefreshToken(admin);

    // Log successful login
    console.log(`✅ Admin login successful: ${username} from IP: ${req.ip}`);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      refreshToken,
      user: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        permissions: admin.permissions
      },
      expiresIn: authService.jwtExpires
    });

  } catch (error) {
    console.error('Admin login error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Login processing failed',
      code: 'LOGIN_ERROR'
    });
  }
});

// ============================================================================
// ADMIN LOGOUT ENDPOINT
// ============================================================================

/**
 * Admin logout endpoint
 */
router.post('/logout', auditLog('admin_logout'), (req, res) => {
  try {
    // In a real implementation, you might invalidate the token
    // For now, we'll just return success
    
    console.log(`📤 Admin logout from IP: ${req.ip}`);
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Admin logout error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Logout processing failed'
    });
  }
});

// ============================================================================
// ADMIN STATUS CHECK
// ============================================================================

/**
 * Check admin authentication status
 */
router.get('/status', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.json({
        authenticated: false,
        message: 'No token provided'
      });
    }

    try {
      const decoded = authService.verifyToken(token);
      
      res.json({
        authenticated: true,
        user: {
          id: decoded.id,
          username: decoded.username,
          role: decoded.role,
          permissions: decoded.permissions
        }
      });
    } catch (tokenError) {
      res.json({
        authenticated: false,
        message: 'Invalid or expired token'
      });
    }
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      authenticated: false,
      message: 'Status check failed'
    });
  }
});

module.exports = router;
