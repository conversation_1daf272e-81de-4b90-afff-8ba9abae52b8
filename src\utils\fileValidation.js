const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

/**
 * File Validation Utilities
 * Centralizes file validation logic to eliminate duplication
 * Extracted from fileUploadService.js and securityMiddleware.js
 */

/**
 * Supported file types and their configurations
 */
const FILE_TYPES = {
  audio: {
    extensions: ['.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg'],
    maxSize: 100 * 1024 * 1024, // 100MB
    mimeTypes: [
      'audio/mpeg',
      'audio/wav',
      'audio/flac',
      'audio/mp4',
      'audio/aac',
      'audio/ogg'
    ]
  },
  video: {
    extensions: ['.mp4', '.mov', '.avi', '.mkv', '.webm'],
    maxSize: 500 * 1024 * 1024, // 500MB
    mimeTypes: [
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/x-matroska',
      'video/webm'
    ]
  },
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
    maxSize: 10 * 1024 * 1024, // 10MB
    mimeTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif'
    ]
  }
};

/**
 * Dangerous filename patterns to block
 * Prevents path traversal and malicious file uploads
 */
const DANGEROUS_PATTERNS = [
  /\.\./,                    // Path traversal
  /[<>:"|?*]/,              // Invalid filename characters
  /^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i, // Windows reserved names
  /^\./,                     // Hidden files starting with dot
  /\.(exe|bat|cmd|scr|pif|com|dll|vbs|js|jar|app|deb|rpm)$/i, // Executable files
  /\.(php|asp|aspx|jsp|py|rb|pl|sh|bash)$/i, // Script files
  /\.(htaccess|htpasswd|config|ini|conf)$/i, // Config files
  /\0/,                      // Null bytes
  /[\x00-\x1f\x7f-\x9f]/,   // Control characters
];

/**
 * Validate file extension
 */
function validateFileExtension(filename, allowedTypes = ['audio', 'video', 'image']) {
  try {
    const ext = path.extname(filename).toLowerCase();
    
    if (!ext) {
      return {
        valid: false,
        error: 'File must have an extension',
        code: 'NO_EXTENSION'
      };
    }

    // Check if extension is allowed for any of the specified types
    for (const type of allowedTypes) {
      if (FILE_TYPES[type] && FILE_TYPES[type].extensions.includes(ext)) {
        return {
          valid: true,
          type,
          extension: ext
        };
      }
    }

    return {
      valid: false,
      error: `File extension '${ext}' is not allowed`,
      code: 'INVALID_EXTENSION',
      allowedExtensions: allowedTypes.flatMap(type => FILE_TYPES[type]?.extensions || [])
    };

  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate file extension',
      code: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Validate file size
 */
function validateFileSize(fileSize, fileType) {
  try {
    if (!fileSize || fileSize <= 0) {
      return {
        valid: false,
        error: 'Invalid file size',
        code: 'INVALID_SIZE'
      };
    }

    const maxSize = FILE_TYPES[fileType]?.maxSize;
    if (!maxSize) {
      return {
        valid: false,
        error: 'Unknown file type for size validation',
        code: 'UNKNOWN_TYPE'
      };
    }

    if (fileSize > maxSize) {
      return {
        valid: false,
        error: `File size (${formatFileSize(fileSize)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`,
        code: 'SIZE_EXCEEDED',
        maxSize,
        actualSize: fileSize
      };
    }

    return {
      valid: true,
      size: fileSize,
      maxSize
    };

  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate file size',
      code: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Validate MIME type
 */
function validateMimeType(mimeType, fileType) {
  try {
    if (!mimeType) {
      return {
        valid: false,
        error: 'MIME type is required',
        code: 'NO_MIME_TYPE'
      };
    }

    const allowedMimeTypes = FILE_TYPES[fileType]?.mimeTypes;
    if (!allowedMimeTypes) {
      return {
        valid: false,
        error: 'Unknown file type for MIME validation',
        code: 'UNKNOWN_TYPE'
      };
    }

    if (!allowedMimeTypes.includes(mimeType)) {
      return {
        valid: false,
        error: `MIME type '${mimeType}' is not allowed for ${fileType} files`,
        code: 'INVALID_MIME_TYPE',
        allowedMimeTypes
      };
    }

    return {
      valid: true,
      mimeType
    };

  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate MIME type',
      code: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Validate filename for security issues
 */
function validateFilename(filename) {
  try {
    if (!filename || typeof filename !== 'string') {
      return {
        valid: false,
        error: 'Filename is required',
        code: 'NO_FILENAME'
      };
    }

    // Check filename length
    if (filename.length > 255) {
      return {
        valid: false,
        error: 'Filename is too long (max 255 characters)',
        code: 'FILENAME_TOO_LONG'
      };
    }

    // Check for dangerous patterns
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(filename)) {
        return {
          valid: false,
          error: 'Filename contains dangerous characters or patterns',
          code: 'DANGEROUS_FILENAME'
        };
      }
    }

    // Additional security checks
    const basename = path.basename(filename);
    if (basename !== filename) {
      return {
        valid: false,
        error: 'Filename cannot contain path separators',
        code: 'PATH_IN_FILENAME'
      };
    }

    return {
      valid: true,
      filename: filename.trim()
    };

  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate filename',
      code: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Generate safe filename
 */
function generateSafeFilename(originalFilename, prefix = '') {
  try {
    // Extract extension
    const ext = path.extname(originalFilename).toLowerCase();
    const basename = path.basename(originalFilename, ext);
    
    // Sanitize basename
    let safeName = basename
      .replace(/[^a-zA-Z0-9\-_\s]/g, '') // Remove special characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .substring(0, 100); // Limit length

    // Add timestamp to ensure uniqueness
    const timestamp = Date.now();
    const randomSuffix = crypto.randomBytes(4).toString('hex');
    
    // Construct final filename
    const finalName = `${prefix}${safeName}_${timestamp}_${randomSuffix}${ext}`;
    
    return {
      success: true,
      filename: finalName,
      originalName: originalFilename
    };

  } catch (error) {
    // Fallback to completely random filename
    const ext = path.extname(originalFilename).toLowerCase();
    const randomName = crypto.randomBytes(16).toString('hex');
    
    return {
      success: true,
      filename: `${prefix}${randomName}${ext}`,
      originalName: originalFilename,
      fallback: true
    };
  }
}

/**
 * Comprehensive file validation
 */
function validateFile(file, allowedTypes = ['audio', 'video', 'image']) {
  try {
    const results = {
      valid: true,
      errors: [],
      warnings: [],
      fileInfo: {}
    };

    // Validate filename
    const filenameResult = validateFilename(file.originalname || file.name);
    if (!filenameResult.valid) {
      results.valid = false;
      results.errors.push(filenameResult.error);
    }

    // Validate extension and determine file type
    const extensionResult = validateFileExtension(file.originalname || file.name, allowedTypes);
    if (!extensionResult.valid) {
      results.valid = false;
      results.errors.push(extensionResult.error);
    } else {
      results.fileInfo.type = extensionResult.type;
      results.fileInfo.extension = extensionResult.extension;
    }

    // Validate file size
    if (extensionResult.valid) {
      const sizeResult = validateFileSize(file.size, extensionResult.type);
      if (!sizeResult.valid) {
        results.valid = false;
        results.errors.push(sizeResult.error);
      } else {
        results.fileInfo.size = sizeResult.size;
        results.fileInfo.maxSize = sizeResult.maxSize;
      }
    }

    // Validate MIME type
    if (extensionResult.valid && file.mimetype) {
      const mimeResult = validateMimeType(file.mimetype, extensionResult.type);
      if (!mimeResult.valid) {
        results.valid = false;
        results.errors.push(mimeResult.error);
      } else {
        results.fileInfo.mimeType = mimeResult.mimeType;
      }
    }

    return results;

  } catch (error) {
    return {
      valid: false,
      errors: ['File validation failed'],
      warnings: [],
      fileInfo: {}
    };
  }
}

/**
 * Format file size for human reading
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if file exists and is accessible
 */
function checkFileAccess(filePath) {
  try {
    fs.accessSync(filePath, fs.constants.F_OK | fs.constants.R_OK);
    return { accessible: true };
  } catch (error) {
    return {
      accessible: false,
      error: error.message
    };
  }
}

/**
 * Get file type configuration
 */
function getFileTypeConfig(type) {
  return FILE_TYPES[type] || null;
}

/**
 * Get all supported file types
 */
function getSupportedFileTypes() {
  return Object.keys(FILE_TYPES);
}

module.exports = {
  validateFile,
  validateFileExtension,
  validateFileSize,
  validateMimeType,
  validateFilename,
  generateSafeFilename,
  formatFileSize,
  checkFileAccess,
  getFileTypeConfig,
  getSupportedFileTypes,
  FILE_TYPES,
  DANGEROUS_PATTERNS
};
