const authService = require('../services/authService');

/**
 * Admin Authentication Middleware
 * Provides consistent JWT token validation across all admin routes
 * Fixes token validation inconsistencies and audience mismatches
 */

/**
 * Main admin authentication middleware
 * Validates JWT tokens and attaches user data to request
 */
const authenticateAdmin = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    const token = authService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No authentication token provided.',
        code: 'NO_TOKEN'
      });
    }

    // Verify token using unified AuthService
    const decoded = authService.verifyToken(token);

    // Validate token type
    if (decoded.type !== 'admin_access') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token type for admin access.',
        code: 'INVALID_TOKEN_TYPE'
      });
    }

    // Attach user data to request
    req.user = decoded;
    req.adminId = decoded.id;
    req.isAuthenticated = true;

    next();
  } catch (error) {
    console.error('Admin authentication error:', error.message);

    // Handle specific JWT errors
    if (error.message.includes('expired')) {
      return res.status(401).json({
        success: false,
        message: 'Authentication token has expired. Please login again.',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.message.includes('Invalid')) {
      return res.status(401).json({
        success: false,
        message: 'Invalid authentication token.',
        code: 'INVALID_TOKEN'
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Authentication verification failed.',
        code: 'AUTH_ERROR'
      });
    }
  }
};

/**
 * Permission-based middleware factory
 * Creates middleware that checks for specific permissions
 */
const requirePermission = (permission = 'admin') => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated first
      if (!req.user || !req.isAuthenticated) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required.',
          code: 'NOT_AUTHENTICATED'
        });
      }

      // Check if user has required permission
      if (!authService.hasPermission(req.user, permission)) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required permission: ${permission}`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission verification failed.',
        code: 'PERMISSION_ERROR'
      });
    }
  };
};

/**
 * Optional authentication middleware
 * Attaches user data if token is present but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authService.extractTokenFromHeader(authHeader);

    if (token) {
      try {
        const decoded = authService.verifyToken(token);
        req.user = decoded;
        req.adminId = decoded.id;
        req.isAuthenticated = true;
      } catch (error) {
        // Token is invalid but we don't fail the request
        req.isAuthenticated = false;
      }
    } else {
      req.isAuthenticated = false;
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    req.isAuthenticated = false;
    next();
  }
};

/**
 * Admin role validation middleware
 * Ensures user has admin role specifically
 */
const requireAdminRole = (req, res, next) => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin role required.',
        code: 'ADMIN_ROLE_REQUIRED'
      });
    }

    next();
  } catch (error) {
    console.error('Admin role check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Role verification failed.',
      code: 'ROLE_ERROR'
    });
  }
};

/**
 * Rate limiting for authentication attempts
 * Prevents brute force attacks
 */
const authRateLimit = (req, res, next) => {
  // This would typically use Redis or in-memory store
  // For now, we'll implement basic rate limiting
  const ip = req.ip || req.connection.remoteAddress;
  const key = `auth_attempts_${ip}`;
  
  // In a real implementation, you'd use Redis or similar
  // For now, just log the attempt
  console.log(`Authentication attempt from IP: ${ip}`);
  
  next();
};

/**
 * Security headers middleware for admin routes
 */
const adminSecurityHeaders = (req, res, next) => {
  // Add security headers for admin routes
  res.setHeader('X-Admin-Route', 'true');
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  
  next();
};

/**
 * Audit logging middleware for admin actions
 */
const auditLog = (action = 'unknown') => {
  return (req, res, next) => {
    try {
      const logData = {
        timestamp: new Date().toISOString(),
        action,
        adminId: req.adminId || 'unknown',
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        path: req.path,
        method: req.method
      };

      console.log('Admin Action:', JSON.stringify(logData));
      
      // In production, you'd save this to a database or log file
      req.auditLog = logData;
      
      next();
    } catch (error) {
      console.error('Audit logging error:', error);
      next(); // Don't fail the request due to logging issues
    }
  };
};

module.exports = {
  authenticateAdmin,
  requirePermission,
  optionalAuth,
  requireAdminRole,
  authRateLimit,
  adminSecurityHeaders,
  auditLog
};
