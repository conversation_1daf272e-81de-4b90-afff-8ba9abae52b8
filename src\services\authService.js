const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

/**
 * Unified Authentication Service
 * Centralizes JWT token management and authentication logic
 * Fixes JWT secret mismatches and token validation inconsistencies
 */
class AuthService {
  constructor() {
    // Standardized JWT configuration
    this.jwtSecret = process.env.ADMIN_JWT_SECRET || 'admin_secret_key_jerry_joo_2024_development';
    this.jwtExpires = process.env.ADMIN_JWT_EXPIRES || '8h';
    
    // Unified JWT parameters - fixes audience/issuer mismatches
    this.jwtConfig = {
      issuer: 'jerryjoo-admin-system',
      audience: 'jerryjoo-admin',
      expiresIn: this.jwtExpires
    };

    // Default admin credentials (for file-system mode)
    this.defaultAdmin = {
      id: 'admin_001',
      username: 'jerryjoo_admin',
      email: '<EMAIL>',
      password: '<PERSON><PERSON><PERSON>2024!Admin', // Will be hashed
      role: 'admin',
      permissions: ['admin', 'upload', 'manage', 'dashboard']
    };
  }

  /**
   * Generate JWT token with standardized configuration
   * @param {Object} payload - User data to include in token
   * @returns {string} JWT token
   */
  generateToken(payload) {
    try {
      // Standardized token payload structure
      const tokenPayload = {
        id: payload.id,
        username: payload.username,
        email: payload.email,
        role: payload.role || 'admin',
        permissions: payload.permissions || ['admin'],
        type: 'admin_access'
      };

      return jwt.sign(tokenPayload, this.jwtSecret, this.jwtConfig);
    } catch (error) {
      console.error('Token generation error:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  /**
   * Verify and decode JWT token with consistent validation
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken(token) {
    try {
      // Use same configuration for verification as generation
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience
      });

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Authentication token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid authentication token');
      } else {
        console.error('Token verification error:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Hash password using bcrypt
   * @param {string} password - Plain text password
   * @returns {string} Hashed password
   */
  async hashPassword(password) {
    try {
      const saltRounds = 12;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      console.error('Password hashing error:', error);
      throw new Error('Failed to hash password');
    }
  }

  /**
   * Compare password with hash
   * @param {string} password - Plain text password
   * @param {string} hash - Hashed password
   * @returns {boolean} Password match result
   */
  async comparePassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('Password comparison error:', error);
      return false;
    }
  }

  /**
   * Authenticate admin user (file-system mode)
   * @param {string} username - Admin username
   * @param {string} password - Admin password
   * @returns {Object|null} Admin user data or null if invalid
   */
  async authenticateAdmin(username, password) {
    try {
      // For file-system mode, use default admin credentials
      if (username === this.defaultAdmin.username && password === this.defaultAdmin.password) {
        // Return admin data without password
        const { password: _, ...adminData } = this.defaultAdmin;
        return adminData;
      }

      return null;
    } catch (error) {
      console.error('Admin authentication error:', error);
      return null;
    }
  }

  /**
   * Validate admin permissions
   * @param {Object} user - User object from token
   * @param {string} requiredPermission - Required permission
   * @returns {boolean} Permission check result
   */
  hasPermission(user, requiredPermission = 'admin') {
    if (!user || !user.permissions) {
      return false;
    }

    return user.permissions.includes(requiredPermission) || user.permissions.includes('admin');
  }

  /**
   * Generate refresh token (for future use)
   * @param {Object} payload - User data
   * @returns {string} Refresh token
   */
  generateRefreshToken(payload) {
    try {
      const refreshPayload = {
        id: payload.id,
        username: payload.username,
        type: 'refresh_token'
      };

      return jwt.sign(refreshPayload, this.jwtSecret, {
        issuer: this.jwtConfig.issuer,
        audience: this.jwtConfig.audience,
        expiresIn: '7d' // Longer expiration for refresh tokens
      });
    } catch (error) {
      console.error('Refresh token generation error:', error);
      throw new Error('Failed to generate refresh token');
    }
  }

  /**
   * Extract token from Authorization header
   * @param {string} authHeader - Authorization header value
   * @returns {string|null} Extracted token or null
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Get JWT configuration for debugging
   * @returns {Object} JWT configuration (without secret)
   */
  getConfig() {
    return {
      issuer: this.jwtConfig.issuer,
      audience: this.jwtConfig.audience,
      expiresIn: this.jwtConfig.expiresIn,
      hasSecret: !!this.jwtSecret
    };
  }
}

// Export singleton instance
module.exports = new AuthService();
