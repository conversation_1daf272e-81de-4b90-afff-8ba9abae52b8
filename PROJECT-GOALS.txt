
JERRYJOO-WEBSITE: Final 14-Day MVP Action Plan
Objective: To construct a functional front-end prototype of the JERRYJOO-WEBSITE. This plan uses the specified repository structure and provides all necessary links to create a tangible product ready for full-stack integration.
Pre-Sprint Checklist (Completed):
    • Project Directory: JERRYJOO-WEBSITE/
    • Player Repositories Imported into PLAYERS/ dir:
        ◦ PLAYERS/audio-player/ (Contains code excerpts and desired element examples)
        ◦ PLAYERS/full-audio-player-code/ (The full AmplitudeJS repository, cloned from https://github.com/serversideup/amplitudejs)
        ◦ PLAYERS/video-player/ (The full Fluid Player repository, cloned from https://github.com/fluid-player/fluid-player)
    • License Files: LICENSE files from both repositories should be copied to the project root to ensure compliance.
Developer's Toolkit (Essential Links):
    • Amplitudejs Docs & Examples: https://521dimensions.com/open-source/amplitudejs/docs/examples/
    • Amplitudejs GitHub Repository: https://github.com/serversideup/amplitudejs
    • Fluid Player Docs: https://docs.fluidplayer.com/docs/overview/
    • Fluid Player GitHub Repository: https://github.com/fluid-player/fluid-player

Phase 1: Foundation & Core Media (Days 1 - 5)
Goal: Integrate the core audio and video technologies in a static but fully functional state, using the provided project structure.
Days 1-2: Action - Implement the Core Audio Player
    • File Targets: audio-release.html, js/audio.js, css/style.css
    • Actions:
        1. HTML Structure: In audio-release.html, build the HTML layout for the player, referencing the code excerpts in the PLAYERS/audio-player/ directory for the desired structure.
        2. Link Library: Link the core AmplitudeJS library files located within PLAYERS/full-audio-player-code/dist/ to your HTML page.
        3. Initialize Player: In js/audio.js, write the script to initialize Amplitude.js with a hard-coded 3-song playlist.
    • ✅ Success Criteria:
        1. The player renders on audio-release.html and can play, pause, and skip tracks, powered by the library from PLAYERS/full-audio-player-code/.
        2. Song metadata updates correctly for the active track.
Day 3: Action - Implement the Core Video Player
    • File Targets: video-release.html, js/video.js
    • Actions:
        1. HTML Structure: In video-release.html, add a <video> tag with a unique ID.
        2. Link & Initialize: Link the Fluid Player library files from PLAYERS/video-player/ and initialize the player on your video tag with a sample .mp4 URL.
    • ✅ Success Criteria:
        1. The video player, powered by the library in PLAYERS/video-player/, renders and all controls are fully functional.
Days 4-5: Action - Build Spotify-Style Synced Lyrics
    • File Target: js/audio.js, audio-release.html
    • Actions:
        1. Data & Display: Create a sample .lrc timed lyric file. Add a scrollable <div> in audio-release.html for the lyrics.
        2. Parsing & Sync: In js/audio.js, write a function to parse the .lrc file. Use the Amplitude.js timeupdate event to find the active lyric, highlight it with CSS, and use element.scrollIntoView() to keep it centered.
    • ✅ Success Criteria:
        1. Lyrics load into their container.
        2. The current lyric is highlighted in real-time as the song plays.
        3. The container scrolls smoothly to follow the active lyric.

Phase 2: Interactivity & Dynamic Data (Days 6 - 10)
Goal: Evolve from static components to a dynamic and interactive user experience.
Days 6-7: Action - Engineer Dynamic Content & Visuals
    • File Target: js/audio.js
    • Actions:
        1. Simulate API: Create a JavaScript function that returns a playlist object. Add UI buttons that call this function to load new playlists into the player without a page refresh.
        2. Integrate Visualizer: Add a <canvas> element and connect it to a visualizer from the AmplitudeJS library, referencing examples in the docs or the PLAYERS/audio-player/ directory.
    • ✅ Success Criteria:
        1. The user can switch between playlists on demand.
        2. A visualizer animates in sync with the audio.
Days 8-9: Action - Simulate User-Curated Playlists
    • File Targets: index.html, js/main.js
    • Actions:
        1. UI: On index.html, display a master list of songs, each with an "Add to My Playlist" button.
        2. Front-End State: In js/main.js, create a global userPlaylist array. The "Add" button's onclick will push song objects into this array.
        3. Playback: Create a "Play My Playlist" button that loads the audio player with the userPlaylist array.
    • ✅ Success Criteria:
        1. A user can build a temporary playlist.
        2. The player can play this custom-generated list.
Day 10: Action - Build Site Architecture & Linking
    • File Targets: merchandise.html, tickets.html, and existing pages.
    • Actions:
        1. Create Pages: Build placeholder merchandise.html and tickets.html files.
        2. Cross-Link: Link audio-release.html to video-release.html (and vice-versa). Link both to merchandise.html.
    • ✅ Success Criteria:
        1. A user can navigate logically between a track's audio, video, and related merchandise pages.

Phase 3: Monetization & Project Finalization (Days 11 - 14)
Goal: Implement the business logic shell and prepare the project for a clean handoff to backend development.
Days 11-12: Action - Construct the Paywall Simulation
    • File Targets: audio-release.html, video-release.html, css/style.css
    • Actions:
        1. UI: Create a "paywall" overlay <div> that hides the player container by default.
        2. Unlock Logic: Write a JavaScript function for a "Pay to Access" button that hides the paywall and reveals the player.
    • ✅ Success Criteria:
        1. All media content is hidden by a paywall on page load.
        2. Clicking the purchase button instantly reveals the player.
Day 13: Action - Scaffold Future Feature UIs
    • File Targets: tickets.html, merchandise.html
    • Actions:
        1. Ticketing UI: On tickets.html, build a static layout for an event page with placeholders for artists, venue, and social links.
        2. Physical Goods UI: On merchandise.html, add sections for "Hard Copies" and "Recommended Record Player Dealers."
    • ✅ Success Criteria:
        1. The UI mockups for future ticketing and e-commerce features are built.
Day 14: Action - Final Review, Documentation & Handoff
    • File Target: README.md
    • Actions:
        1. Code Review: Add comments to all complex code sections.
        2. Create README.md: Author a comprehensive readme file. Document the project structure, including the roles of audio-player, full-audio-player-code, and video-player. Include the GitHub links in this file for future reference.
        3. Backend Handoff Notes: Create a clear section in the README listing all simulation points that need to be replaced with real backend logic (e.g., "Replace static playlist function with a database API call," "Wire purchase button to a real payment gateway").
    • ✅ Success Criteria:
        1. The project is clean, commented, and fully functional as per the 14-day plan.
        2. The README.md provides a perfect, context-aware starting point for a backend developer.
