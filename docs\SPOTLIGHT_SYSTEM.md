# Spotlight Toggle System Documentation

## Overview

The Jerry Joo website now features a comprehensive toggleable spotlight effect system that allows users to switch between two distinct viewing modes:

### State 1: Spotlight Mode (Default)
- **Dark overlay** covering the entire website (opacity 0.5-0.6)
- **Red spotlight effect** that follows the mouse cursor
- Content is only clearly visible within the spotlight radius
- Areas outside the spotlight are darkened and barely visible
- Creates an atmospheric, immersive viewing experience

### State 2: Normal Mode
- **No dark overlay** - website appears in its original bright state
- **No spotlight effect** - all content is fully visible
- Standard website experience without any visual effects
- Traditional web browsing experience

## Features

### ✅ **Core Functionality**
- **Synchronized Toggle**: Both dark overlay AND spotlight effect are enabled/disabled together
- **State Persistence**: Toggle state persists across page navigation using localStorage
- **Smooth Transitions**: Seamless transitions between modes with CSS animations
- **Performance Optimized**: Efficient mouse tracking and rendering

### ✅ **User Interface**
- **Toggle Button**: Located in top-right corner with sun/moon icons
- **Visual Feedback**: <PERSON><PERSON> changes color and shows pulse animation when spotlight is active
- **Tooltip**: Hover tooltip shows current mode and keyboard shortcut
- **Accessibility**: Full ARIA support and keyboard navigation

### ✅ **Interaction Methods**
1. **Click Toggle**: Click the toggle button in the top-right corner
2. **Keyboard Shortcut**: Press `Ctrl+L` (or `Cmd+L` on Mac) to toggle
3. **Visual Notification**: Brief notification appears when using keyboard shortcut

### ✅ **Technical Implementation**
- **React Context**: Global state management with `SpotlightContext`
- **TypeScript**: Full type safety throughout the system
- **CSS Animations**: Smooth transitions and pulse effects
- **Mouse Tracking**: Real-time cursor position tracking
- **Local Storage**: Persistent user preferences

## File Structure

```
src/
├── contexts/
│   └── SpotlightContext.tsx          # Global spotlight state management
├── components/
│   ├── SpotlightToggle.tsx           # Toggle button component
│   └── MouseSpotlight.tsx            # Spotlight effect renderer
├── pages/
│   └── SpotlightTestPage.tsx         # Test page for spotlight functionality
├── index.css                         # Global CSS animations and styles
└── App.tsx                           # Provider integration
```

## Usage

### For Users
1. **Toggle via Button**: Click the toggle button in the top-right corner
2. **Toggle via Keyboard**: Press `Ctrl+L` (Windows/Linux) or `Cmd+L` (Mac)
3. **Mode Persistence**: Your preference is saved and restored on page reload
4. **Cross-Page Consistency**: Setting applies to all pages on the website

### For Developers

#### Using the Spotlight Context
```tsx
import { useSpotlight } from '../contexts/SpotlightContext';

const MyComponent = () => {
  const { isSpotlightEnabled, toggleSpotlight, mousePosition } = useSpotlight();
  
  return (
    <div>
      <p>Spotlight is {isSpotlightEnabled ? 'ON' : 'OFF'}</p>
      <p>Mouse at: {mousePosition.x}, {mousePosition.y}</p>
      <button onClick={toggleSpotlight}>Toggle Spotlight</button>
    </div>
  );
};
```

#### Adding the Provider
```tsx
import { SpotlightProvider } from './contexts/SpotlightContext';

const App = () => (
  <SpotlightProvider>
    {/* Your app components */}
  </SpotlightProvider>
);
```

## Configuration

### Spotlight Settings
The spotlight effect can be customized in `MouseSpotlight.tsx`:

```tsx
// Spotlight radius and intensity
background: `radial-gradient(circle 400px at ${mousePosition.x}px ${mousePosition.y}px, ...)`

// Red spotlight color and opacity
rgba(234, 56, 76, 0.09) // Adjust opacity for intensity
```

### Dark Overlay Settings
Dark overlay opacity can be adjusted in `MouseSpotlight.tsx`:

```tsx
// Main dark overlay
background: rgba(0, 0, 0, 0.4) // Adjust for darkness level

// Secondary overlay
background: rgba(0, 0, 0, 0.35) // Additional darkness layer
```

### Animation Settings
Transition speeds can be modified in `index.css`:

```css
/* Global transitions */
body {
  transition: background-color 0.3s ease-in-out;
}

/* Spotlight transitions */
.spotlight-effect {
  transition: all 0.3s ease-in-out;
}
```

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## Performance Notes

- **Optimized Mouse Tracking**: Uses requestAnimationFrame for smooth performance
- **CSS Hardware Acceleration**: GPU-accelerated transitions and effects
- **Minimal Re-renders**: Context optimizations prevent unnecessary updates
- **Memory Management**: Proper cleanup of event listeners and animations

## Testing

Visit `/spotlight-test` to access the comprehensive test page that demonstrates:
- Real-time spotlight state monitoring
- Mouse position tracking
- Manual toggle controls
- Feature descriptions
- Interactive test content

## Troubleshooting

### Common Issues

1. **Spotlight not following mouse**: Check if JavaScript is enabled and context is properly wrapped
2. **State not persisting**: Verify localStorage is available and not blocked
3. **Performance issues**: Ensure hardware acceleration is enabled in browser
4. **Toggle not visible**: Check z-index conflicts and CSS specificity

### Debug Mode
Add this to browser console to monitor spotlight state:
```javascript
window.addEventListener('spotlightToggle', (e) => {
  console.log('Spotlight toggled:', e.detail);
});
```

## Future Enhancements

Potential improvements for future versions:
- [ ] Customizable spotlight colors
- [ ] Multiple spotlight shapes (circle, square, etc.)
- [ ] Spotlight size adjustment
- [ ] Animation speed controls
- [ ] Mobile touch support optimization
- [ ] Accessibility improvements for screen readers

---

**Implementation Complete**: The spotlight toggle system is fully functional and ready for production use on the Jerry Joo website.
