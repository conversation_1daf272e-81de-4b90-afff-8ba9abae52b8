const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import middleware
const { adminSecurityHeaders } = require('./middleware/adminAuth');

// Import routes
const myballsRoutes = require('./routes/myballs');
const authRoutes = require('./routes/auth');

/**
 * Jerry Joo Music Website - Backend Server
 * Unified Express application with fixed authentication system
 */

const app = express();
const PORT = process.env.PORT || 5000;

// ============================================================================
// SECURITY MIDDLEWARE
// ============================================================================

// Helmet for security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https:"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'", "https:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https:"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:8080',
    'http://localhost:3000',
    process.env.FRONTEND_URL || 'http://localhost:8080'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression
app.use(compression());

// ============================================================================
// LOGGING MIDDLEWARE
// ============================================================================

// Morgan logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// ============================================================================
// BODY PARSING MIDDLEWARE
// ============================================================================

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// ============================================================================
// STATIC FILE SERVING
// ============================================================================

// Serve static files from TRACKS directory
const tracksPath = process.env.TRACKS_BASE_PATH || './TRACKS';
if (fs.existsSync(tracksPath)) {
  app.use('/tracks', express.static(tracksPath));
  console.log(`✅ Serving static files from: ${tracksPath}`);
} else {
  console.log(`⚠️ TRACKS directory not found: ${tracksPath}`);
}

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// ============================================================================
// HEALTH CHECK ENDPOINT
// ============================================================================

app.get('/api/health', (req, res) => {
  const healthStatus = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    services: {
      server: 'running',
      authentication: 'active',
      fileSystem: fs.existsSync(tracksPath) ? 'connected' : 'disconnected'
    }
  };

  res.json(healthStatus);
});

// ============================================================================
// AUTHENTICATION ROUTES
// ============================================================================

// Admin authentication routes (file-system based)
app.use('/myballs', adminSecurityHeaders, myballsRoutes);

// API authentication routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', authRoutes);

// ============================================================================
// API ROUTES (Protected)
// ============================================================================

// Dashboard stats endpoint
app.get('/api/admin/dashboard/stats', 
  require('./middleware/adminAuth').authenticateAdmin,
  (req, res) => {
    try {
      const stats = {
        totalTracks: 0,
        totalAlbums: 0,
        totalUploads: 0,
        recentActivity: [],
        systemStatus: {
          server: 'running',
          database: 'file-system-mode',
          storage: 'available'
        },
        user: {
          username: req.user.username,
          role: req.user.role,
          permissions: req.user.permissions
        }
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Dashboard stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard statistics'
      });
    }
  }
);

// ============================================================================
// ERROR HANDLING MIDDLEWARE
// ============================================================================

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path,
    method: req.method
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(isDevelopment && { stack: error.stack }),
    timestamp: new Date().toISOString()
  });
});

// ============================================================================
// SERVER STARTUP
// ============================================================================

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log('\n🎵 Jerry Joo Music Website - Backend Server');
  console.log('='.repeat(50));
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📁 Tracks directory: ${tracksPath}`);
  console.log(`🔐 Admin panel: http://localhost:${PORT}/myballs`);
  console.log(`🔗 API health: http://localhost:${PORT}/api/health`);
  console.log(`📊 Admin API: http://localhost:${PORT}/api/admin`);
  console.log('='.repeat(50));
});

module.exports = app;
