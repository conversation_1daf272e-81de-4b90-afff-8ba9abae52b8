const express = require('express');
const authService = require('../services/authService');
const { authenticateAdmin, auditLog } = require('../middleware/adminAuth');

const router = express.Router();

/**
 * API Authentication Routes (/api/auth, /api/admin)
 * Standardized JWT authentication for API endpoints
 * Fixes token validation inconsistencies and provides unified auth endpoints
 */

// ============================================================================
// ADMIN LOGIN (API VERSION)
// ============================================================================

/**
 * Admin login endpoint for API clients
 * POST /api/admin/auth/login
 */
router.post('/auth/login', auditLog('api_admin_login'), async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required',
        code: 'MISSING_CREDENTIALS'
      });
    }

    // Authenticate admin using unified AuthService
    const admin = await authService.authenticateAdmin(username, password);

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Generate standardized JWT token
    const token = authService.generateToken(admin);
    const refreshToken = authService.generateRefreshToken(admin);

    // Log successful login
    console.log(`✅ API Admin login successful: ${username} from IP: ${req.ip}`);

    res.json({
      success: true,
      message: 'Authentication successful',
      data: {
        token,
        refreshToken,
        user: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          permissions: admin.permissions
        },
        expiresIn: authService.jwtExpires,
        tokenType: 'Bearer'
      }
    });

  } catch (error) {
    console.error('API admin login error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Authentication processing failed',
      code: 'AUTH_ERROR'
    });
  }
});

// ============================================================================
// ADMIN LOGOUT (API VERSION)
// ============================================================================

/**
 * Admin logout endpoint for API clients
 * POST /api/admin/auth/logout
 */
router.post('/auth/logout', authenticateAdmin, auditLog('api_admin_logout'), (req, res) => {
  try {
    console.log(`📤 API Admin logout: ${req.user.username} from IP: ${req.ip}`);
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('API admin logout error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Logout processing failed'
    });
  }
});

// ============================================================================
// ADMIN PROFILE
// ============================================================================

/**
 * Get admin profile information
 * GET /api/admin/auth/profile
 */
router.get('/auth/profile', authenticateAdmin, (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          username: req.user.username,
          email: req.user.email,
          role: req.user.role,
          permissions: req.user.permissions
        },
        authConfig: authService.getConfig()
      }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch profile'
    });
  }
});

// ============================================================================
// TOKEN VALIDATION
// ============================================================================

/**
 * Validate token endpoint
 * POST /api/admin/auth/validate
 */
router.post('/auth/validate', (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required',
        valid: false
      });
    }

    try {
      const decoded = authService.verifyToken(token);
      
      res.json({
        success: true,
        valid: true,
        data: {
          user: {
            id: decoded.id,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions
          },
          expiresAt: new Date(decoded.exp * 1000).toISOString()
        }
      });
    } catch (tokenError) {
      res.json({
        success: false,
        valid: false,
        message: tokenError.message
      });
    }
  } catch (error) {
    console.error('Token validation error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Token validation failed'
    });
  }
});

// ============================================================================
// REFRESH TOKEN
// ============================================================================

/**
 * Refresh authentication token
 * POST /api/admin/auth/refresh
 */
router.post('/auth/refresh', (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    try {
      // Verify refresh token
      const decoded = authService.verifyToken(refreshToken);
      
      if (decoded.type !== 'refresh_token') {
        return res.status(400).json({
          success: false,
          message: 'Invalid refresh token type'
        });
      }

      // Generate new access token
      const newToken = authService.generateToken({
        id: decoded.id,
        username: decoded.username,
        role: 'admin',
        permissions: ['admin']
      });

      res.json({
        success: true,
        data: {
          token: newToken,
          expiresIn: authService.jwtExpires,
          tokenType: 'Bearer'
        }
      });
    } catch (tokenError) {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token'
      });
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Token refresh failed'
    });
  }
});

// ============================================================================
// AUTHENTICATION STATUS
// ============================================================================

/**
 * Check authentication status
 * GET /api/admin/auth/status
 */
router.get('/auth/status', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.json({
        success: true,
        authenticated: false,
        message: 'No token provided'
      });
    }

    try {
      const decoded = authService.verifyToken(token);
      
      res.json({
        success: true,
        authenticated: true,
        data: {
          user: {
            id: decoded.id,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions
          },
          expiresAt: new Date(decoded.exp * 1000).toISOString()
        }
      });
    } catch (tokenError) {
      res.json({
        success: true,
        authenticated: false,
        message: tokenError.message
      });
    }
  } catch (error) {
    console.error('Auth status check error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Status check failed'
    });
  }
});

// ============================================================================
// AUTHENTICATION CONFIGURATION
// ============================================================================

/**
 * Get authentication configuration (for debugging)
 * GET /api/admin/auth/config
 */
router.get('/auth/config', authenticateAdmin, (req, res) => {
  try {
    const config = authService.getConfig();
    
    res.json({
      success: true,
      data: {
        jwtConfig: config,
        environment: process.env.NODE_ENV || 'development',
        serverTime: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Config fetch error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch configuration'
    });
  }
});

module.exports = router;
