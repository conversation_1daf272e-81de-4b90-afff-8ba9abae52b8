# ============================================================================
# JERRY JOO WEBSITE - SECURITY & PRIVACY PROTECTION
# ============================================================================

# CRITICAL: Environment Variables (Contains Sensitive Data)
.env
.env.local
.env.development
.env.production
.env.test

# CRITICAL: Payment & API Keys
config/keys.js
config/secrets.js
*.key
*.pem
*.p12

# CRITICAL: User Data & Payments
Payment/
TRACKS/
uploads/
downloads/
temp/
*.receipt
*.transaction

# Node.js Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build Outputs
dist/
build/
.next/
out/

# Development Files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log
*.log.*

# Runtime Data
pids/
*.pid
*.seed
*.pid.lock

# Coverage & Testing
coverage/
.nyc_output/
.coverage/

# Cache Directories
.cache/
.parcel-cache/
.eslintcache

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
tmp/
temp/
*.tmp
*.temp

# Database Files
*.db
*.sqlite
*.sqlite3

# Backup Files
*.backup
*.bak
*.old

# ============================================================================
# SECURITY NOTES:
# - Never commit .env files (contain API keys, passwords, secrets)
# - Never commit payment data or user information
# - Never commit private keys or certificates
# - Always use environment variables for sensitive configuration
# ============================================================================
