# 🚀 Jerry <PERSON> Music Website - Production Deployment Guide

**Target Platform:** Ubuntu Server 20.04/22.04 LTS  
**Deployment Type:** Full-stack web application with MongoDB  
**Security Level:** Production-grade with SSL/TLS

---

## 📋 **Prerequisites**

### **Server Requirements**
- **OS:** Ubuntu Server 20.04 LTS or newer
- **RAM:** Minimum 2GB (Recommended 4GB+)
- **Storage:** Minimum 20GB SSD (Recommended 50GB+)
- **CPU:** 2+ cores recommended
- **Network:** Public IP address with ports 80, 443, 22 open

### **Domain & DNS**
- Domain name pointing to server IP
- DNS A record configured
- Optional: Subdomain for admin panel

---

## 🔧 **Step 1: Server Setup & Security**

### **1.1 Initial Server Configuration**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Create application user
sudo adduser jerryjoo
sudo usermod -aG sudo jerryjoo

# Switch to application user
su - jerryjoo
```

### **1.2 SSH Security Hardening**
```bash
# Generate SSH key pair (on local machine)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to server
ssh-copy-id jerryjoo@your-server-ip

# Configure SSH (on server)
sudo nano /etc/ssh/sshd_config
```

**SSH Configuration:**
```
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AllowUsers jerryjoo
```

```bash
# Restart SSH service
sudo systemctl restart ssh
```

### **1.3 Firewall Configuration**
```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 2222/tcp  # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

---

## 📦 **Step 2: Install Dependencies**

### **2.1 Node.js Installation**
```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 9.x.x
```

### **2.2 MongoDB Installation**
```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start and enable MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify installation
sudo systemctl status mongod
```

### **2.3 Nginx Installation**
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

### **2.4 PM2 Process Manager**
```bash
# Install PM2 globally
sudo npm install -g pm2

# Configure PM2 to start on boot
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u jerryjoo --hp /home/<USER>
```

---

## 🗄️ **Step 3: Database Setup**

### **3.1 MongoDB Security Configuration**
```bash
# Connect to MongoDB
mongosh

# Create admin user
use admin
db.createUser({
  user: "admin",
  pwd: "STRONG_ADMIN_PASSWORD_HERE",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})

# Create application database and user
use jerryjoo_music
db.createUser({
  user: "jerryjoo_app",
  pwd: "STRONG_APP_PASSWORD_HERE",
  roles: ["readWrite"]
})

# Exit MongoDB shell
exit
```

### **3.2 Enable MongoDB Authentication**
```bash
# Edit MongoDB configuration
sudo nano /etc/mongod.conf
```

**Add to mongod.conf:**
```yaml
security:
  authorization: enabled

net:
  bindIp: 127.0.0.1
  port: 27017
```

```bash
# Restart MongoDB
sudo systemctl restart mongod
```

### **3.3 Database Backup Setup**
```bash
# Create backup directory
sudo mkdir -p /var/backups/mongodb
sudo chown jerryjoo:jerryjoo /var/backups/mongodb

# Create backup script
nano /home/<USER>/backup-db.sh
```

**Backup Script:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/mongodb"
DB_NAME="jerryjoo_music"

mongodump --host localhost:27017 --db $DB_NAME --username jerryjoo_app --password "STRONG_APP_PASSWORD_HERE" --out $BACKUP_DIR/backup_$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -type d -name "backup_*" -mtime +7 -exec rm -rf {} \;
```

```bash
# Make script executable
chmod +x /home/<USER>/backup-db.sh

# Add to crontab for daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup-db.sh
```

---

## 📁 **Step 4: Application Deployment**

### **4.1 Clone Repository**
```bash
# Navigate to home directory
cd /home/<USER>

# Clone the repository
git clone https://github.com/your-username/jerryjoo-website.git
cd jerryjoo-website

# Install dependencies
npm install
```

### **4.2 Environment Configuration**
```bash
# Create production environment file
nano .env.production
```

**Production Environment Variables:**
```env
# Server Configuration
NODE_ENV=production
PORT=5000
HOST=0.0.0.0

# Database Configuration
MONGODB_URI=******************************************************************************
MONGODB_DB_NAME=jerryjoo_music

# JWT Configuration
ADMIN_JWT_SECRET=GENERATE_STRONG_SECRET_HERE_64_CHARS_MIN
ADMIN_JWT_EXPIRES=8h

# File Storage
TRACKS_BASE_PATH=/home/<USER>/jerryjoo-website/TRACKS
UPLOADS_PATH=/home/<USER>/jerryjoo-website/uploads

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Payment Configuration
PESAPAL_CONSUMER_KEY=your-pesapal-consumer-key
PESAPAL_CONSUMER_SECRET=your-pesapal-consumer-secret
PESAPAL_ENVIRONMENT=live

# Security
CORS_ORIGIN=https://yourdomain.com
SESSION_SECRET=GENERATE_STRONG_SESSION_SECRET_HERE

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/jerryjoo/app.log
```

### **4.3 Create Enhanced Directory Structure**
```bash
# Enhanced music file storage architecture
mkdir -p TRACKS/{singles,albums,previews}/{$(date +%Y)}
mkdir -p uploads/{temp,processing,quarantine}
mkdir -p artwork/{covers,thumbnails}
mkdir -p logs

# Create comprehensive log directory structure
sudo mkdir -p /var/log/jerryjoo/{app,security,payments}
sudo mkdir -p /var/log/jerryjoo/payments/{transactions,failed,archive}
sudo mkdir -p /var/log/jerryjoo/payments/transactions/{$(date +%Y)}/{$(date +%m)}/{$(date +%d)}

# Set ownership
sudo chown -R jerryjoo:jerryjoo /var/log/jerryjoo

# Set proper permissions for music files
chmod 755 TRACKS/ artwork/
chmod 750 uploads/
find TRACKS/ -type d -exec chmod 755 {} \;
find TRACKS/ -type f -exec chmod 644 {} \;

# Set secure permissions for uploads
find uploads/ -type d -exec chmod 750 {} \;
find uploads/ -type f -exec chmod 640 {} \;

# Set secure permissions for payment logs
sudo chmod 750 /var/log/jerryjoo/payments
sudo find /var/log/jerryjoo/payments -type d -exec chmod 750 {} \;
sudo find /var/log/jerryjoo/payments -type f -exec chmod 640 {} \;

# Secure environment file
chmod 644 .env.production
```

### **4.4 Create Automated Cleanup Scripts**
```bash
# Create temp directory cleanup script
nano /home/<USER>/cleanup-temp.sh
```

**Temp Cleanup Script:**
```bash
#!/bin/bash
# Cleanup temporary files older than 24 hours
TEMP_DIR="/home/<USER>/jerryjoo-website/uploads/temp"
QUARANTINE_DIR="/home/<USER>/jerryjoo-website/uploads/quarantine"
LOG_FILE="/var/log/jerryjoo/cleanup.log"

# Log cleanup start
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting temp cleanup" >> $LOG_FILE

# Remove temp files older than 24 hours
find $TEMP_DIR -type f -mtime +1 -delete 2>> $LOG_FILE
find $TEMP_DIR -type d -empty -delete 2>> $LOG_FILE

# Remove quarantine files older than 7 days
find $QUARANTINE_DIR -type f -mtime +7 -delete 2>> $LOG_FILE

# Log cleanup completion
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Temp cleanup completed" >> $LOG_FILE
```

```bash
# Make script executable
chmod +x /home/<USER>/cleanup-temp.sh

# Add to crontab (run every hour)
crontab -e
# Add: 0 * * * * /home/<USER>/cleanup-temp.sh
```

### **4.4 Build Frontend (if applicable)**
```bash
# Build React frontend for production
npm run build

# Verify build output
ls -la dist/
```

---

## 🔒 **Step 5: SSL Certificate Setup**

### **5.1 Install Certbot**
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx
```

### **5.2 Obtain SSL Certificate**
```bash
# Stop Nginx temporarily
sudo systemctl stop nginx

# Obtain certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Start Nginx
sudo systemctl start nginx
```

### **5.3 Configure Auto-renewal**
```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🌐 **Step 6: Nginx Configuration**

### **6.1 Create Nginx Configuration**
```bash
# Create site configuration
sudo nano /etc/nginx/sites-available/jerryjoo-music
```

**Nginx Configuration:**
```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;

# Upstream backend
upstream backend {
    server 127.0.0.1:5000;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Enhanced Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; media-src 'self'; connect-src 'self'; font-src 'self'; object-src 'none'; frame-src 'none';" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Enhanced file serving security - Block dangerous file types
    location ~* \.(php|jsp|asp|sh|py|pl|exe|bat|cmd|scr|pif|com|dll|vbs|js|jar|app|deb|rpm)$ {
        deny all;
        access_log /var/log/nginx/blocked_files.log;
    }

    # Static file serving with enhanced security
    location /tracks/ {
        alias /home/<USER>/jerryjoo-website/TRACKS/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Block hidden files and backup files
        location ~ /\. { deny all; access_log off; log_not_found off; }
        location ~ ~$ { deny all; access_log off; log_not_found off; }
        location ~ \.bak$ { deny all; access_log off; log_not_found off; }

        # Only allow specific audio/video file types
        location ~* \.(mp3|wav|flac|m4a|aac|ogg|mp4|mov|avi|mkv|webm)$ {
            add_header X-Content-Type-Options nosniff;
            add_header X-Frame-Options DENY;
        }
    }

    location /uploads/ {
        alias /home/<USER>/jerryjoo-website/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Block access to processing and quarantine directories
        location ~ ^/uploads/(processing|quarantine)/ {
            deny all;
            access_log /var/log/nginx/blocked_access.log;
        }

        # Block hidden files and backup files
        location ~ /\. { deny all; access_log off; log_not_found off; }
        location ~ ~$ { deny all; access_log off; log_not_found off; }
    }

    # Artwork serving with image-specific security
    location /artwork/ {
        alias /home/<USER>/jerryjoo-website/artwork/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Only allow image files
        location ~* \.(jpg|jpeg|png|webp|gif|svg)$ {
            add_header X-Content-Type-Options nosniff;
        }

        # Block everything else
        location ~ ^/artwork/.*\.(?!jpg|jpeg|png|webp|gif|svg$) {
            deny all;
        }
    }

    # Admin panel with rate limiting
    location /myballs {
        limit_req zone=admin burst=10 nodelay;
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API endpoints with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Frontend application
    location / {
        root /home/<USER>/jerryjoo-website/dist;
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
}
```

### **6.2 Enable Site Configuration**
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/jerryjoo-music /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

---

## 🚀 **Step 7: Process Management**

### **7.1 Create PM2 Ecosystem File**
```bash
# Create PM2 configuration
nano ecosystem.config.js
```

**PM2 Configuration:**
```javascript
module.exports = {
  apps: [{
    name: 'jerryjoo-backend',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/var/log/jerryjoo/error.log',
    out_file: '/var/log/jerryjoo/out.log',
    log_file: '/var/log/jerryjoo/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### **7.2 Start Application with PM2**
```bash
# Start application in production mode
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Check application status
pm2 status
pm2 logs jerryjoo-backend
```

### **7.3 PM2 Monitoring Setup**
```bash
# Install PM2 monitoring (optional)
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
pm2 set pm2-logrotate:compress true
```

---

## 🔐 **Step 8: Security Hardening**

### **8.1 Fail2Ban Installation**
```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Create custom configuration
sudo nano /etc/fail2ban/jail.local
```

**Fail2Ban Configuration:**
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
```

```bash
# Start and enable Fail2Ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

### **8.2 System Security Updates**
```bash
# Enable automatic security updates
sudo apt install -y unattended-upgrades

# Configure automatic updates
sudo nano /etc/apt/apt.conf.d/50unattended-upgrades
```

**Auto-update Configuration:**
```
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
```

### **8.3 File Permissions Security**
```bash
# Set secure file permissions
chmod 600 .env.production
chmod 755 src/
chmod 644 src/*.js
chmod 755 TRACKS/ artwork/
chmod 750 uploads/

# Set ownership
sudo chown -R jerryjoo:jerryjoo /home/<USER>/jerryjoo-website
```

### **8.4 Anti-Malware Protection**
```bash
# Install ClamAV anti-malware
sudo apt install -y clamav clamav-daemon clamav-freshclam

# Update virus definitions
sudo freshclam

# Enable and start services
sudo systemctl enable clamav-freshclam
sudo systemctl start clamav-freshclam
sudo systemctl enable clamav-daemon
sudo systemctl start clamav-daemon

# Create malware scanning script
nano /home/<USER>/scan-uploads.sh
```

**Malware Scanning Script:**
```bash
#!/bin/bash
UPLOAD_DIR="/home/<USER>/jerryjoo-website/uploads"
QUARANTINE_DIR="/home/<USER>/jerryjoo-website/uploads/quarantine"
LOG_FILE="/var/log/jerryjoo/security/malware-scan.log"

# Create quarantine directory if it doesn't exist
mkdir -p $QUARANTINE_DIR

# Scan uploads directory
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting malware scan" >> $LOG_FILE

# Scan for malware
SCAN_RESULT=$(clamscan -r --infected --remove --move=$QUARANTINE_DIR $UPLOAD_DIR/temp 2>&1)
SCAN_EXIT_CODE=$?

# Log results
if [ $SCAN_EXIT_CODE -eq 0 ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Scan completed - No threats found" >> $LOG_FILE
elif [ $SCAN_EXIT_CODE -eq 1 ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ALERT: Malware detected and quarantined" >> $LOG_FILE
    echo "$SCAN_RESULT" >> $LOG_FILE
    # Send alert email (configure as needed)
    echo "Malware detected on Jerry Joo server" | mail -s "SECURITY ALERT" <EMAIL>
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Scan error occurred" >> $LOG_FILE
    echo "$SCAN_RESULT" >> $LOG_FILE
fi
```

```bash
# Make script executable
chmod +x /home/<USER>/scan-uploads.sh

# Add to crontab (scan every 30 minutes)
crontab -e
# Add: */30 * * * * /home/<USER>/scan-uploads.sh
```

### **8.5 Payment Transaction File Logging**
```bash
# Create payment logging directory structure (already created in step 4.3)
# Verify structure exists
ls -la /var/log/jerryjoo/payments/

# Create payment logging configuration
nano /home/<USER>/payment-logger.js
```

**Payment Logger Configuration:**
```javascript
const fs = require('fs').promises;
const path = require('path');

class PaymentLogger {
  constructor() {
    this.baseDir = '/var/log/jerryjoo/payments';
    this.transactionDir = path.join(this.baseDir, 'transactions');
    this.failedDir = path.join(this.baseDir, 'failed');
  }

  async logTransaction(transactionData) {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0].replace(/-/g, '');
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '');

    const yearDir = path.join(this.transactionDir, now.getFullYear().toString());
    const monthDir = path.join(yearDir, (now.getMonth() + 1).toString().padStart(2, '0'));
    const dayDir = path.join(monthDir, now.getDate().toString().padStart(2, '0'));

    // Ensure directory exists
    await fs.mkdir(dayDir, { recursive: true });

    const filename = `transaction_${dateStr}_${timeStr}_${transactionData.id}.json`;
    const filepath = path.join(dayDir, filename);

    const logEntry = {
      timestamp: now.toISOString(),
      transaction_id: transactionData.id,
      amount: transactionData.amount,
      currency: transactionData.currency,
      status: transactionData.status,
      payment_method: transactionData.payment_method,
      customer_id: transactionData.customer_id,
      ip_address: transactionData.ip_address,
      user_agent: transactionData.user_agent,
      pesapal_tracking_id: transactionData.pesapal_tracking_id
    };

    await fs.writeFile(filepath, JSON.stringify(logEntry, null, 2));

    // Update daily summary
    await this.updateDailySummary(dayDir, transactionData);
  }

  async logFailedTransaction(failureData) {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0].replace(/-/g, '');
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '');

    const filename = `failed_transaction_${dateStr}_${timeStr}_${failureData.attempt_id}.json`;
    const filepath = path.join(this.failedDir, filename);

    const logEntry = {
      timestamp: now.toISOString(),
      attempt_id: failureData.attempt_id,
      error_code: failureData.error_code,
      error_message: failureData.error_message,
      amount: failureData.amount,
      customer_id: failureData.customer_id,
      ip_address: failureData.ip_address,
      retry_count: failureData.retry_count
    };

    await fs.writeFile(filepath, JSON.stringify(logEntry, null, 2));
  }

  async updateDailySummary(dayDir, transactionData) {
    const summaryFile = path.join(dayDir, `daily_summary_${path.basename(dayDir.replace(/\//g, ''))}.json`);

    let summary = {
      date: new Date().toISOString().split('T')[0],
      total_transactions: 0,
      total_amount: 0,
      successful_transactions: 0,
      failed_transactions: 0,
      payment_methods: {}
    };

    try {
      const existingSummary = await fs.readFile(summaryFile, 'utf8');
      summary = JSON.parse(existingSummary);
    } catch (error) {
      // File doesn't exist, use default summary
    }

    summary.total_transactions++;
    summary.total_amount += parseFloat(transactionData.amount);

    if (transactionData.status === 'completed') {
      summary.successful_transactions++;
    } else {
      summary.failed_transactions++;
    }

    const method = transactionData.payment_method || 'unknown';
    summary.payment_methods[method] = (summary.payment_methods[method] || 0) + 1;

    await fs.writeFile(summaryFile, JSON.stringify(summary, null, 2));
  }
}

module.exports = PaymentLogger;
```

```bash
# Set proper permissions for payment logger
chmod 644 /home/<USER>/payment-logger.js
```

### **8.6 Payment Log Rotation Configuration**
```bash
# Create payment log rotation configuration
sudo nano /etc/logrotate.d/jerryjoo-payments
```

**Payment Log Rotation Configuration:**
```
/var/log/jerryjoo/payments/transactions/*/*/*/*.json {
    monthly
    missingok
    rotate 36
    compress
    delaycompress
    notifempty
    create 640 jerryjoo jerryjoo
    postrotate
        # Archive old transaction logs
        find /var/log/jerryjoo/payments/transactions -name "*.json.gz" -mtime +90 -exec mv {} /var/log/jerryjoo/payments/archive/ \;
    endscript
}

/var/log/jerryjoo/payments/failed/*.json {
    weekly
    missingok
    rotate 12
    compress
    delaycompress
    notifempty
    create 640 jerryjoo jerryjoo
}
```

### **8.7 Payment Logging Verification**
```bash
# Create payment logging test script
nano /home/<USER>/test-payment-logging.sh
```

**Payment Logging Test Script:**
```bash
#!/bin/bash
echo "Testing payment logging functionality..."

# Test transaction logging
node -e "
const PaymentLogger = require('./payment-logger.js');
const logger = new PaymentLogger();

const testTransaction = {
  id: 'test_' + Date.now(),
  amount: '25.00',
  currency: 'KES',
  status: 'completed',
  payment_method: 'mpesa',
  customer_id: 'test_customer',
  ip_address: '127.0.0.1',
  user_agent: 'test-agent',
  pesapal_tracking_id: 'test_tracking'
};

logger.logTransaction(testTransaction).then(() => {
  console.log('✅ Transaction logging test successful');
}).catch(err => {
  console.error('❌ Transaction logging test failed:', err);
});
"

# Verify log files were created
echo "Checking log file creation..."
TODAY=$(date +%Y/%m/%d)
if [ -d "/var/log/jerryjoo/payments/transactions/$TODAY" ]; then
    echo "✅ Transaction log directory created"
    ls -la "/var/log/jerryjoo/payments/transactions/$TODAY"
else
    echo "❌ Transaction log directory not found"
fi
```

```bash
# Make test script executable and run it
chmod +x /home/<USER>/test-payment-logging.sh
./test-payment-logging.sh
```

---

## 📊 **Step 9: Monitoring & Logging**

### **9.1 Enhanced System Monitoring Setup**
```bash
# Install monitoring tools including file system monitoring
sudo apt install -y htop iotop nethogs inotify-tools jq

# Create comprehensive monitoring script
nano /home/<USER>/enhanced-monitor.sh
```

**Enhanced Monitoring Script:**
```bash
#!/bin/bash
LOG_FILE="/var/log/jerryjoo/system-monitor.log"
SECURITY_LOG="/var/log/jerryjoo/security/security-monitor.log"
PAYMENT_LOG="/var/log/jerryjoo/payments"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# System metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')
DISK_USAGE_NUMERIC=$(df / | awk 'NR==2{printf "%.0f", $3/$2 * 100}')

# Application status
PM2_STATUS=$(pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null || echo "unknown")

# File system monitoring
TRACKS_SIZE=$(du -sh /home/<USER>/jerryjoo-website/TRACKS 2>/dev/null | awk '{print $1}')
UPLOADS_SIZE=$(du -sh /home/<USER>/jerryjoo-website/uploads 2>/dev/null | awk '{print $1}')

# Payment log analysis
TODAY=$(date +%Y/%m/%d)
PAYMENT_DIR="$PAYMENT_LOG/transactions/$TODAY"
if [ -d "$PAYMENT_DIR" ]; then
    DAILY_TRANSACTIONS=$(find "$PAYMENT_DIR" -name "transaction_*.json" | wc -l)
    FAILED_TRANSACTIONS=$(find "$PAYMENT_LOG/failed" -name "failed_transaction_$(date +%Y%m%d)_*.json" | wc -l)
else
    DAILY_TRANSACTIONS=0
    FAILED_TRANSACTIONS=0
fi

# Log system metrics
echo "[$DATE] CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}, App: ${PM2_STATUS}, Tracks: ${TRACKS_SIZE}, Uploads: ${UPLOADS_SIZE}, Transactions: ${DAILY_TRANSACTIONS}, Failed: ${FAILED_TRANSACTIONS}" >> $LOG_FILE

# Check for alerts
if [ "$DISK_USAGE_NUMERIC" -gt 85 ]; then
    echo "[$DATE] ALERT: Disk usage high (${DISK_USAGE})" >> $SECURITY_LOG
    # Trigger cleanup if disk usage > 85%
    /home/<USER>/cleanup-temp.sh
fi

if [ "$FAILED_TRANSACTIONS" -gt 5 ]; then
    echo "[$DATE] ALERT: High number of failed transactions today ($FAILED_TRANSACTIONS)" >> $SECURITY_LOG
fi

# Check for unauthorized file access
SUSPICIOUS_ACCESS=$(grep -c "403\|404" /var/log/nginx/blocked_access.log 2>/dev/null || echo "0")
if [ "$SUSPICIOUS_ACCESS" -gt 10 ]; then
    echo "[$DATE] ALERT: High number of blocked access attempts ($SUSPICIOUS_ACCESS)" >> $SECURITY_LOG
fi
```

```bash
# Make script executable
chmod +x /home/<USER>/enhanced-monitor.sh

# Add to crontab (every 5 minutes)
crontab -e
# Add: */5 * * * * /home/<USER>/enhanced-monitor.sh
```

### **9.2 File System Monitoring with Inotify**
```bash
# Create file system monitoring script
nano /home/<USER>/fs-monitor.sh
```

**File System Monitoring Script:**
```bash
#!/bin/bash
WATCH_DIRS="/home/<USER>/jerryjoo-website/uploads /home/<USER>/jerryjoo-website/TRACKS"
LOG_FILE="/var/log/jerryjoo/security/fs-monitor.log"

# Function to log file system events
log_event() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Monitor file system events
inotifywait -m -r -e create,delete,modify,move --format '%w%f %e %T' --timefmt '%Y-%m-%d %H:%M:%S' $WATCH_DIRS | while read FILE EVENT TIME
do
    # Log all events
    log_event "File: $FILE, Event: $EVENT, Time: $TIME"

    # Check for suspicious patterns
    case $EVENT in
        CREATE)
            # Check if created file has suspicious extension
            if [[ $FILE =~ \.(php|jsp|asp|sh|py|pl|exe|bat|cmd|scr)$ ]]; then
                log_event "ALERT: Suspicious file created: $FILE"
                # Move to quarantine
                mv "$FILE" "/home/<USER>/jerryjoo-website/uploads/quarantine/" 2>/dev/null
            fi
            ;;
        DELETE)
            # Log file deletions for audit
            log_event "INFO: File deleted: $FILE"
            ;;
        MODIFY)
            # Check for rapid modifications (potential attack)
            RECENT_MODS=$(grep -c "MODIFY.*$(basename $FILE)" $LOG_FILE | tail -10)
            if [ "$RECENT_MODS" -gt 5 ]; then
                log_event "ALERT: Rapid modifications detected on: $FILE"
            fi
            ;;
    esac
done &

# Save PID for later termination
echo $! > /var/run/fs-monitor.pid
log_event "File system monitoring started (PID: $!)"
```

```bash
# Make script executable
chmod +x /home/<USER>/fs-monitor.sh

# Create systemd service for file system monitoring
sudo nano /etc/systemd/system/jerryjoo-fs-monitor.service
```

**File System Monitor Service:**
```ini
[Unit]
Description=Jerry Joo File System Monitor
After=network.target

[Service]
Type=forking
User=jerryjoo
ExecStart=/home/<USER>/fs-monitor.sh
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/fs-monitor.pid
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start the service
sudo systemctl enable jerryjoo-fs-monitor
sudo systemctl start jerryjoo-fs-monitor
```

### **9.3 Payment Log Analysis and Alerting**
```bash
# Create payment analysis script
nano /home/<USER>/payment-analysis.sh
```

**Payment Analysis Script:**
```bash
#!/bin/bash
PAYMENT_LOG_DIR="/var/log/jerryjoo/payments"
ALERT_LOG="/var/log/jerryjoo/security/payment-alerts.log"
TODAY=$(date +%Y%m%d)
YESTERDAY=$(date -d "yesterday" +%Y%m%d)

# Function to send alert
send_alert() {
    local message="$1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] PAYMENT ALERT: $message" >> $ALERT_LOG
    # Send email alert (configure as needed)
    echo "$message" | mail -s "Jerry Joo Payment Alert" <EMAIL> 2>/dev/null
}

# Analyze failed transactions
FAILED_TODAY=$(find "$PAYMENT_LOG_DIR/failed" -name "failed_transaction_${TODAY}_*.json" | wc -l)
FAILED_YESTERDAY=$(find "$PAYMENT_LOG_DIR/failed" -name "failed_transaction_${YESTERDAY}_*.json" | wc -l)

if [ "$FAILED_TODAY" -gt 10 ]; then
    send_alert "High number of failed transactions today: $FAILED_TODAY"
fi

if [ "$FAILED_TODAY" -gt $((FAILED_YESTERDAY * 2)) ] && [ "$FAILED_YESTERDAY" -gt 0 ]; then
    send_alert "Failed transactions doubled compared to yesterday: $FAILED_TODAY vs $FAILED_YESTERDAY"
fi

# Analyze transaction patterns
TODAY_DIR="$PAYMENT_LOG_DIR/transactions/$(date +%Y/%m/%d)"
if [ -d "$TODAY_DIR" ]; then
    # Check for unusual transaction amounts
    HIGH_AMOUNT_COUNT=$(find "$TODAY_DIR" -name "*.json" -exec grep -l '"amount".*[5-9][0-9][0-9][0-9]' {} \; | wc -l)
    if [ "$HIGH_AMOUNT_COUNT" -gt 5 ]; then
        send_alert "Unusual number of high-value transactions detected: $HIGH_AMOUNT_COUNT"
    fi

    # Check for rapid transactions from same IP
    SUSPICIOUS_IPS=$(find "$TODAY_DIR" -name "*.json" -exec grep -h '"ip_address"' {} \; | sort | uniq -c | awk '$1 > 10 {print $2}')
    if [ ! -z "$SUSPICIOUS_IPS" ]; then
        send_alert "Suspicious IP activity detected: $SUSPICIOUS_IPS"
    fi
fi

# Check disk space for payment logs
PAYMENT_LOG_SIZE=$(du -sm "$PAYMENT_LOG_DIR" | awk '{print $1}')
if [ "$PAYMENT_LOG_SIZE" -gt 1000 ]; then  # Alert if > 1GB
    send_alert "Payment log directory size is large: ${PAYMENT_LOG_SIZE}MB"
fi
```

```bash
# Make script executable
chmod +x /home/<USER>/payment-analysis.sh

# Add to crontab (run every hour)
crontab -e
# Add: 0 * * * * /home/<USER>/payment-analysis.sh
```

### **9.2 Application Logging Configuration**
```bash
# Create log rotation configuration
sudo nano /etc/logrotate.d/jerryjoo
```

**Log Rotation Configuration:**
```
/var/log/jerryjoo/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 jerryjoo jerryjoo
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## 🧪 **Step 10: Testing & Verification**

### **10.1 Health Check Script**
```bash
# Create health check script
nano /home/<USER>/health-check.sh
```

**Health Check Script:**
```bash
#!/bin/bash
DOMAIN="https://yourdomain.com"
ADMIN_PANEL="https://yourdomain.com/myballs"
API_HEALTH="https://yourdomain.com/api/health"

echo "🔍 Jerry Joo Music Website - Health Check"
echo "=========================================="

# Check main website
echo -n "Main Website: "
if curl -s -o /dev/null -w "%{http_code}" $DOMAIN | grep -q "200"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check admin panel
echo -n "Admin Panel: "
if curl -s -o /dev/null -w "%{http_code}" $ADMIN_PANEL | grep -q "200"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check API health
echo -n "API Health: "
if curl -s $API_HEALTH | grep -q "OK"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check database connection
echo -n "Database: "
if mongosh --quiet --eval "db.adminCommand('ping')" jerryjoo_music > /dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check PM2 status
echo -n "Application: "
if pm2 jlist | jq -r '.[0].pm2_env.status' | grep -q "online"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

echo "=========================================="
echo "Health check completed at $(date)"
```

```bash
# Make script executable
chmod +x /home/<USER>/health-check.sh

# Run health check
./health-check.sh
```

### **10.2 Performance Testing**
```bash
# Install Apache Bench for load testing
sudo apt install -y apache2-utils

# Test website performance
ab -n 100 -c 10 https://yourdomain.com/
ab -n 50 -c 5 https://yourdomain.com/api/health

# Test admin authentication
curl -X POST https://yourdomain.com/myballs/login \
  -H "Content-Type: application/json" \
  -d '{"username":"jerryjoo_admin","password":"JerryJoo2024!Admin"}'
```

---

## 🔄 **Step 11: Backup & Recovery**

### **11.1 Enhanced Hybrid Storage Backup Strategy**
```bash
# Create comprehensive backup script with hybrid storage support
nano /home/<USER>/enhanced-backup.sh
```

**Enhanced Backup Script:**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/jerryjoo"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/home/<USER>/jerryjoo-website"
LOG_FILE="/var/log/jerryjoo/backup.log"

# RTO: 4 hours, RPO: 1 hour
# Backup retention: Daily (30 days), Weekly (12 weeks), Monthly (12 months)

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# Create backup directories
mkdir -p $BACKUP_DIR/{daily,weekly,monthly,incremental}

log_message "Starting enhanced backup process"

# 1. Database Backup with verification
log_message "Backing up MongoDB database..."
mongodump --host localhost:27017 --db jerryjoo_music \
  --username jerryjoo_app --password "STRONG_APP_PASSWORD_HERE" \
  --out $BACKUP_DIR/daily/db_backup_$DATE

# Verify database backup
if [ $? -eq 0 ]; then
    log_message "✅ Database backup successful"
    # Test restore capability
    DB_SIZE=$(du -sh $BACKUP_DIR/daily/db_backup_$DATE | awk '{print $1}')
    log_message "Database backup size: $DB_SIZE"
else
    log_message "❌ Database backup failed"
    exit 1
fi

# 2. Application Files Backup
log_message "Backing up application files..."
tar -czf $BACKUP_DIR/daily/app_backup_$DATE.tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=logs \
  --exclude=uploads/temp \
  --exclude=uploads/quarantine \
  $APP_DIR

# 3. Incremental Music Files Backup (only changed files)
log_message "Performing incremental backup of music files..."
LAST_BACKUP_FILE="$BACKUP_DIR/.last_music_backup"
MUSIC_BACKUP_DIR="$BACKUP_DIR/incremental/music_$DATE"

if [ -f "$LAST_BACKUP_FILE" ]; then
    LAST_BACKUP_TIME=$(cat $LAST_BACKUP_FILE)
    log_message "Last music backup: $LAST_BACKUP_TIME"

    # Find files modified since last backup
    find $APP_DIR/TRACKS -type f -newer "$LAST_BACKUP_FILE" > /tmp/changed_music_files.txt
    CHANGED_COUNT=$(wc -l < /tmp/changed_music_files.txt)

    if [ $CHANGED_COUNT -gt 0 ]; then
        log_message "Found $CHANGED_COUNT changed music files"
        mkdir -p $MUSIC_BACKUP_DIR
        tar -czf $MUSIC_BACKUP_DIR/changed_tracks_$DATE.tar.gz -T /tmp/changed_music_files.txt
    else
        log_message "No music files changed since last backup"
    fi
else
    log_message "First music backup - backing up all files"
    tar -czf $BACKUP_DIR/daily/all_tracks_$DATE.tar.gz $APP_DIR/TRACKS
fi

# Update last backup timestamp
touch $LAST_BACKUP_FILE

# 4. Payment Logs Backup (90-day retention)
log_message "Backing up payment logs..."
tar -czf $BACKUP_DIR/daily/payment_logs_$DATE.tar.gz \
  /var/log/jerryjoo/payments/transactions \
  /var/log/jerryjoo/payments/failed

# 5. Security Logs Backup
log_message "Backing up security logs..."
tar -czf $BACKUP_DIR/daily/security_logs_$DATE.tar.gz \
  /var/log/jerryjoo/security \
  /var/log/nginx/blocked_*.log

# 6. Configuration Backup
log_message "Backing up configurations..."
mkdir -p $BACKUP_DIR/daily/config_$DATE
cp /etc/nginx/sites-available/jerryjoo-music $BACKUP_DIR/daily/config_$DATE/
cp $APP_DIR/.env.production $BACKUP_DIR/daily/config_$DATE/
cp /etc/logrotate.d/jerryjoo* $BACKUP_DIR/daily/config_$DATE/
cp /etc/systemd/system/jerryjoo-*.service $BACKUP_DIR/daily/config_$DATE/

# 7. Backup Verification and Integrity Check
log_message "Verifying backup integrity..."
BACKUP_SIZE=$(du -sh $BACKUP_DIR/daily | awk '{print $1}')
log_message "Total backup size: $BACKUP_SIZE"

# Test random file extraction
TEST_FILE=$(find $BACKUP_DIR/daily -name "*.tar.gz" | head -1)
if [ ! -z "$TEST_FILE" ]; then
    tar -tzf "$TEST_FILE" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_message "✅ Backup integrity check passed"
    else
        log_message "❌ Backup integrity check failed"
        exit 1
    fi
fi

# 8. Backup Rotation Strategy
log_message "Applying backup rotation policy..."

# Daily backups (keep 30 days)
find $BACKUP_DIR/daily -name "*backup*" -mtime +30 -delete

# Weekly backups (every Sunday, keep 12 weeks)
if [ $(date +%u) -eq 7 ]; then
    log_message "Creating weekly backup..."
    cp -r $BACKUP_DIR/daily/db_backup_$DATE $BACKUP_DIR/weekly/
    cp $BACKUP_DIR/daily/app_backup_$DATE.tar.gz $BACKUP_DIR/weekly/
    find $BACKUP_DIR/weekly -name "*backup*" -mtime +84 -delete  # 12 weeks
fi

# Monthly backups (first day of month, keep 12 months)
if [ $(date +%d) -eq 01 ]; then
    log_message "Creating monthly backup..."
    cp -r $BACKUP_DIR/daily/db_backup_$DATE $BACKUP_DIR/monthly/
    cp $BACKUP_DIR/daily/app_backup_$DATE.tar.gz $BACKUP_DIR/monthly/
    tar -czf $BACKUP_DIR/monthly/full_tracks_$(date +%Y%m).tar.gz $APP_DIR/TRACKS
    find $BACKUP_DIR/monthly -name "*backup*" -mtime +365 -delete  # 12 months
fi

# 9. Cleanup incremental backups (keep 7 days)
find $BACKUP_DIR/incremental -name "*" -mtime +7 -delete

# 10. Generate backup report
BACKUP_REPORT="$BACKUP_DIR/backup_report_$DATE.txt"
cat > $BACKUP_REPORT << EOF
Jerry Joo Music Website - Backup Report
Generated: $(date)
Backup ID: $DATE

Database Backup: ✅ $(ls -lh $BACKUP_DIR/daily/db_backup_$DATE | awk '{print $5}')
Application Backup: ✅ $(ls -lh $BACKUP_DIR/daily/app_backup_$DATE.tar.gz | awk '{print $5}')
Payment Logs: ✅ $(ls -lh $BACKUP_DIR/daily/payment_logs_$DATE.tar.gz | awk '{print $5}')
Security Logs: ✅ $(ls -lh $BACKUP_DIR/daily/security_logs_$DATE.tar.gz | awk '{print $5}')
Configuration: ✅ $(du -sh $BACKUP_DIR/daily/config_$DATE | awk '{print $1}')

Total Backup Size: $BACKUP_SIZE
Backup Location: $BACKUP_DIR/daily

RTO: 4 hours
RPO: 1 hour
Retention: 30 days (daily), 12 weeks (weekly), 12 months (monthly)

Backup Status: SUCCESSFUL
EOF

log_message "Backup completed successfully - Report: $BACKUP_REPORT"

# 11. Optional: Upload to cloud storage (uncomment if needed)
# log_message "Uploading to cloud storage..."
# aws s3 sync $BACKUP_DIR/daily s3://your-backup-bucket/jerryjoo/daily/ --delete
# aws s3 sync $BACKUP_DIR/weekly s3://your-backup-bucket/jerryjoo/weekly/ --delete
# aws s3 sync $BACKUP_DIR/monthly s3://your-backup-bucket/jerryjoo/monthly/ --delete
```

```bash
# Make script executable
chmod +x /home/<USER>/enhanced-backup.sh

# Add to crontab (daily at 3 AM)
crontab -e
# Add: 0 3 * * * /home/<USER>/enhanced-backup.sh
```

### **11.2 Disaster Recovery Procedures**
```bash
# Create comprehensive disaster recovery script
nano /home/<USER>/disaster-recovery.sh
```

**Disaster Recovery Script:**
```bash
#!/bin/bash
# Jerry Joo Music Website - Disaster Recovery Script
# RTO: 4 hours, RPO: 1 hour

BACKUP_DIR="/var/backups/jerryjoo"
APP_DIR="/home/<USER>/jerryjoo-website"
LOG_FILE="/var/log/jerryjoo/disaster-recovery.log"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# Function to list available backups
list_backups() {
    echo "Available backups:"
    echo "Daily backups:"
    ls -la $BACKUP_DIR/daily/db_backup_* 2>/dev/null | tail -10
    echo "Weekly backups:"
    ls -la $BACKUP_DIR/weekly/db_backup_* 2>/dev/null | tail -5
    echo "Monthly backups:"
    ls -la $BACKUP_DIR/monthly/db_backup_* 2>/dev/null | tail -3
}

# Function to restore database
restore_database() {
    local backup_path="$1"

    if [ ! -d "$backup_path" ]; then
        log_message "❌ Backup path not found: $backup_path"
        return 1
    fi

    log_message "🔄 Starting database recovery from: $backup_path"

    # Stop application
    log_message "Stopping application..."
    pm2 stop jerryjoo-backend

    # Drop existing database (with confirmation)
    log_message "⚠️ Dropping existing database..."
    mongosh --quiet -u jerryjoo_app -p "STRONG_APP_PASSWORD" jerryjoo_music --eval "db.dropDatabase()"

    # Restore database
    log_message "Restoring database..."
    mongorestore --host localhost:27017 --db jerryjoo_music \
      --username jerryjoo_app --password "STRONG_APP_PASSWORD" \
      "$backup_path/jerryjoo_music"

    if [ $? -eq 0 ]; then
        log_message "✅ Database restored successfully"
        return 0
    else
        log_message "❌ Database restore failed"
        return 1
    fi
}

# Function to restore application files
restore_application() {
    local backup_file="$1"

    if [ ! -f "$backup_file" ]; then
        log_message "❌ Backup file not found: $backup_file"
        return 1
    fi

    log_message "🔄 Starting application recovery from: $backup_file"

    # Create backup of current application
    log_message "Creating backup of current application..."
    mv "$APP_DIR" "${APP_DIR}_backup_$(date +%Y%m%d_%H%M%S)"

    # Extract application backup
    log_message "Extracting application backup..."
    mkdir -p "$APP_DIR"
    tar -xzf "$backup_file" -C /home/<USER>/

    # Install dependencies
    log_message "Installing dependencies..."
    cd "$APP_DIR"
    npm install --production

    if [ $? -eq 0 ]; then
        log_message "✅ Application restored successfully"
        return 0
    else
        log_message "❌ Application restore failed"
        return 1
    fi
}

# Function to restore music files
restore_music_files() {
    local backup_file="$1"

    if [ ! -f "$backup_file" ]; then
        log_message "❌ Music backup file not found: $backup_file"
        return 1
    fi

    log_message "🔄 Starting music files recovery from: $backup_file"

    # Backup current music files
    if [ -d "$APP_DIR/TRACKS" ]; then
        log_message "Backing up current music files..."
        mv "$APP_DIR/TRACKS" "${APP_DIR}/TRACKS_backup_$(date +%Y%m%d_%H%M%S)"
    fi

    # Extract music files
    log_message "Extracting music files..."
    tar -xzf "$backup_file" -C "$APP_DIR"

    # Set proper permissions
    chmod 755 "$APP_DIR/TRACKS"
    find "$APP_DIR/TRACKS" -type d -exec chmod 755 {} \;
    find "$APP_DIR/TRACKS" -type f -exec chmod 644 {} \;

    log_message "✅ Music files restored successfully"
    return 0
}

# Function to restore configuration
restore_configuration() {
    local config_dir="$1"

    if [ ! -d "$config_dir" ]; then
        log_message "❌ Configuration backup not found: $config_dir"
        return 1
    fi

    log_message "🔄 Starting configuration recovery from: $config_dir"

    # Restore Nginx configuration
    if [ -f "$config_dir/jerryjoo-music" ]; then
        log_message "Restoring Nginx configuration..."
        sudo cp "$config_dir/jerryjoo-music" /etc/nginx/sites-available/
        sudo nginx -t
        if [ $? -eq 0 ]; then
            sudo systemctl reload nginx
            log_message "✅ Nginx configuration restored"
        else
            log_message "❌ Nginx configuration invalid"
            return 1
        fi
    fi

    # Restore environment configuration
    if [ -f "$config_dir/.env.production" ]; then
        log_message "Restoring environment configuration..."
        cp "$config_dir/.env.production" "$APP_DIR/"
        chmod 600 "$APP_DIR/.env.production"
        log_message "✅ Environment configuration restored"
    fi

    # Restore systemd services
    if ls "$config_dir"/*.service 1> /dev/null 2>&1; then
        log_message "Restoring systemd services..."
        sudo cp "$config_dir"/*.service /etc/systemd/system/
        sudo systemctl daemon-reload
        log_message "✅ Systemd services restored"
    fi

    return 0
}

# Function to perform full system recovery
full_recovery() {
    local backup_date="$1"

    if [ -z "$backup_date" ]; then
        echo "Usage: full_recovery YYYYMMDD_HHMMSS"
        list_backups
        return 1
    fi

    log_message "🚨 Starting FULL SYSTEM RECOVERY for backup: $backup_date"
    log_message "⚠️ This will replace ALL current data with backup data"

    # Confirm recovery
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log_message "Recovery cancelled by user"
        return 1
    fi

    # Stop all services
    log_message "Stopping all services..."
    pm2 stop all
    sudo systemctl stop nginx

    # Restore database
    if ! restore_database "$BACKUP_DIR/daily/db_backup_$backup_date"; then
        log_message "❌ Database recovery failed - aborting"
        return 1
    fi

    # Restore application
    if ! restore_application "$BACKUP_DIR/daily/app_backup_$backup_date.tar.gz"; then
        log_message "❌ Application recovery failed - aborting"
        return 1
    fi

    # Restore configuration
    if ! restore_configuration "$BACKUP_DIR/daily/config_$backup_date"; then
        log_message "❌ Configuration recovery failed - aborting"
        return 1
    fi

    # Start services
    log_message "Starting services..."
    sudo systemctl start nginx
    cd "$APP_DIR"
    pm2 start ecosystem.config.js --env production

    # Verify recovery
    sleep 10
    if pm2 jlist | jq -r '.[0].pm2_env.status' | grep -q "online"; then
        log_message "✅ FULL RECOVERY COMPLETED SUCCESSFULLY"
        log_message "🎉 Jerry Joo Music Website is back online!"
        return 0
    else
        log_message "❌ Recovery verification failed - manual intervention required"
        return 1
    fi
}

# Function to create rollback point
create_rollback_point() {
    local rollback_name="$1"

    if [ -z "$rollback_name" ]; then
        rollback_name="manual_$(date +%Y%m%d_%H%M%S)"
    fi

    log_message "📸 Creating rollback point: $rollback_name"

    # Create rollback directory
    ROLLBACK_DIR="/var/backups/jerryjoo/rollback/$rollback_name"
    mkdir -p "$ROLLBACK_DIR"

    # Backup current state
    mongodump --host localhost:27017 --db jerryjoo_music \
      --username jerryjoo_app --password "STRONG_APP_PASSWORD" \
      --out "$ROLLBACK_DIR/db_backup"

    tar -czf "$ROLLBACK_DIR/app_backup.tar.gz" \
      --exclude=node_modules --exclude=.git --exclude=logs "$APP_DIR"

    cp -r /etc/nginx/sites-available/jerryjoo-music "$ROLLBACK_DIR/"
    cp "$APP_DIR/.env.production" "$ROLLBACK_DIR/"

    log_message "✅ Rollback point created: $ROLLBACK_DIR"
}

# Main script logic
case "$1" in
    "list")
        list_backups
        ;;
    "database")
        restore_database "$2"
        ;;
    "application")
        restore_application "$2"
        ;;
    "music")
        restore_music_files "$2"
        ;;
    "config")
        restore_configuration "$2"
        ;;
    "full")
        full_recovery "$2"
        ;;
    "rollback-point")
        create_rollback_point "$2"
        ;;
    *)
        echo "Jerry Joo Music Website - Disaster Recovery Tool"
        echo "Usage: $0 {list|database|application|music|config|full|rollback-point} [backup_id]"
        echo ""
        echo "Commands:"
        echo "  list                    - List available backups"
        echo "  database <backup_path>  - Restore database only"
        echo "  application <backup>    - Restore application only"
        echo "  music <backup>          - Restore music files only"
        echo "  config <config_dir>     - Restore configuration only"
        echo "  full <backup_date>      - Full system recovery"
        echo "  rollback-point [name]   - Create rollback point"
        echo ""
        echo "Examples:"
        echo "  $0 list"
        echo "  $0 full 20250124_030000"
        echo "  $0 rollback-point before_update"
        ;;
esac
```

```bash
# Make disaster recovery script executable
chmod +x /home/<USER>/disaster-recovery.sh

# Create recovery documentation
nano /home/<USER>/RECOVERY_PROCEDURES.md
```

**Recovery Documentation:**
```markdown
# Jerry Joo Music Website - Recovery Procedures

## Quick Recovery Commands

### List Available Backups
```bash
./disaster-recovery.sh list
```

### Full System Recovery (RTO: 4 hours)
```bash
# Create rollback point first
./disaster-recovery.sh rollback-point before_recovery

# Perform full recovery
./disaster-recovery.sh full YYYYMMDD_HHMMSS
```

### Partial Recovery Options

#### Database Only
```bash
./disaster-recovery.sh database /var/backups/jerryjoo/daily/db_backup_YYYYMMDD_HHMMSS
```

#### Application Only
```bash
./disaster-recovery.sh application /var/backups/jerryjoo/daily/app_backup_YYYYMMDD_HHMMSS.tar.gz
```

#### Music Files Only
```bash
./disaster-recovery.sh music /var/backups/jerryjoo/daily/all_tracks_YYYYMMDD_HHMMSS.tar.gz
```

#### Configuration Only
```bash
./disaster-recovery.sh config /var/backups/jerryjoo/daily/config_YYYYMMDD_HHMMSS
```

## Emergency Procedures

### 1. Complete System Failure
1. Boot from backup server or restore VM
2. Run: `./disaster-recovery.sh full LATEST_BACKUP_DATE`
3. Verify all services: `./production-verification.sh`
4. Update DNS if IP changed

### 2. Database Corruption
1. Stop application: `pm2 stop jerryjoo-backend`
2. Restore database: `./disaster-recovery.sh database LATEST_DB_BACKUP`
3. Start application: `pm2 start jerryjoo-backend`
4. Verify: `mongosh jerryjoo_music --eval "db.stats()"`

### 3. Application Corruption
1. Create rollback point: `./disaster-recovery.sh rollback-point emergency`
2. Restore application: `./disaster-recovery.sh application LATEST_APP_BACKUP`
3. Restart services: `pm2 restart all`

### 4. Configuration Issues
1. Restore configuration: `./disaster-recovery.sh config LATEST_CONFIG`
2. Test Nginx: `sudo nginx -t`
3. Reload services: `sudo systemctl reload nginx`

## Recovery Time Objectives (RTO)
- Database Recovery: 30 minutes
- Application Recovery: 1 hour
- Full System Recovery: 4 hours
- Configuration Recovery: 15 minutes

## Recovery Point Objectives (RPO)
- Database: 1 hour (hourly backups)
- Files: 24 hours (daily backups)
- Configuration: 24 hours (daily backups)

## Verification After Recovery
Always run the verification script after any recovery:
```bash
./production-verification.sh
```

## Rollback Procedures
If recovery fails or causes issues:
```bash
# Restore from rollback point
./disaster-recovery.sh full rollback_point_name
```
```

---

## ✅ **Step 12: Final Verification Checklist**

### **12.1 Comprehensive Production Readiness Checklist**
```bash
# Create comprehensive verification script
nano /home/<USER>/production-verification.sh
```

**Production Verification Script:**
```bash
#!/bin/bash
echo "🎵 Jerry Joo Music Website - Comprehensive Production Verification"
echo "=================================================================="

VERIFICATION_LOG="/var/log/jerryjoo/verification.log"
ERRORS=0

# Function to log and display results
verify_step() {
    local step_name="$1"
    local command="$2"
    local expected="$3"

    echo -n "Testing $step_name: "
    result=$(eval "$command" 2>&1)

    if [[ "$result" == *"$expected"* ]] || [ $? -eq 0 ]; then
        echo "✅ PASS"
        echo "[$(date)] ✅ $step_name: PASS" >> $VERIFICATION_LOG
    else
        echo "❌ FAIL"
        echo "[$(date)] ❌ $step_name: FAIL - $result" >> $VERIFICATION_LOG
        ((ERRORS++))
    fi
}

echo "Starting comprehensive verification at $(date)" > $VERIFICATION_LOG

# 1. Core Services Verification
echo -e "\n🔧 Core Services:"
verify_step "Nginx Service" "sudo systemctl is-active nginx" "active"
verify_step "MongoDB Service" "sudo systemctl is-active mongod" "active"
verify_step "PM2 Application" "pm2 jlist | jq -r '.[0].pm2_env.status'" "online"
verify_step "File System Monitor" "sudo systemctl is-active jerryjoo-fs-monitor" "active"

# 2. SSL/TLS Verification
echo -e "\n🔒 SSL/TLS Security:"
verify_step "SSL Certificate Valid" "echo | openssl s_client -servername yourdomain.com -connect yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates" "notAfter"
verify_step "HTTPS Redirect" "curl -s -o /dev/null -w '%{http_code}' http://yourdomain.com" "301"
verify_step "Security Headers" "curl -s -I https://yourdomain.com | grep -i 'strict-transport-security'" "max-age"

# 3. Authentication System Verification
echo -e "\n🔐 Authentication System:"
# Test admin login (replace with actual credentials)
AUTH_RESPONSE=$(curl -s -X POST https://yourdomain.com/myballs/login \
  -H "Content-Type: application/json" \
  -d '{"username":"NEW_ADMIN_USERNAME","password":"NEW_ADMIN_PASSWORD"}')

if [[ "$AUTH_RESPONSE" == *"success"* ]] && [[ "$AUTH_RESPONSE" == *"token"* ]]; then
    echo "✅ Admin Authentication: PASS"
    echo "[$(date)] ✅ Admin Authentication: PASS" >> $VERIFICATION_LOG

    # Extract token for further testing
    TOKEN=$(echo $AUTH_RESPONSE | jq -r '.token')

    # Test protected endpoint
    PROTECTED_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" https://yourdomain.com/api/admin/dashboard/stats)
    if [[ "$PROTECTED_RESPONSE" == *"success"* ]]; then
        echo "✅ Protected Endpoint Access: PASS"
        echo "[$(date)] ✅ Protected Endpoint Access: PASS" >> $VERIFICATION_LOG
    else
        echo "❌ Protected Endpoint Access: FAIL"
        echo "[$(date)] ❌ Protected Endpoint Access: FAIL" >> $VERIFICATION_LOG
        ((ERRORS++))
    fi
else
    echo "❌ Admin Authentication: FAIL"
    echo "[$(date)] ❌ Admin Authentication: FAIL - $AUTH_RESPONSE" >> $VERIFICATION_LOG
    ((ERRORS++))
fi

# 4. Database Connectivity
echo -e "\n🗄️ Database Connectivity:"
verify_step "MongoDB Connection" "mongosh --quiet --eval 'db.adminCommand(\"ping\")' jerryjoo_music" "ok"
verify_step "Database Authentication" "mongosh --quiet -u jerryjoo_app -p STRONG_APP_PASSWORD --eval 'db.runCommand({connectionStatus: 1})' jerryjoo_music" "ok"

# 5. File Storage Verification
echo -e "\n📁 File Storage System:"
verify_step "TRACKS Directory" "ls -d /home/<USER>/jerryjoo-website/TRACKS" "TRACKS"
verify_step "Uploads Directory" "ls -d /home/<USER>/jerryjoo-website/uploads" "uploads"
verify_step "Payment Logs Directory" "ls -d /var/log/jerryjoo/payments" "payments"
verify_step "Security Logs Directory" "ls -d /var/log/jerryjoo/security" "security"

# Test file upload (create test file)
echo "test content" > /tmp/test_upload.txt
UPLOAD_RESPONSE=$(curl -s -X POST https://yourdomain.com/api/admin/files/test \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@/tmp/test_upload.txt")
rm /tmp/test_upload.txt

# 6. Security Features Verification
echo -e "\n🛡️ Security Features:"
verify_step "Anti-Malware Service" "sudo systemctl is-active clamav-daemon" "active"
verify_step "Fail2Ban Service" "sudo systemctl is-active fail2ban" "active"
verify_step "Firewall Status" "sudo ufw status" "active"

# Test malware scanning
echo 'X5O!P%@AP[4\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*' > /tmp/eicar.txt
SCAN_RESULT=$(clamscan /tmp/eicar.txt 2>&1)
rm /tmp/eicar.txt
if [[ "$SCAN_RESULT" == *"FOUND"* ]]; then
    echo "✅ Malware Detection: PASS"
    echo "[$(date)] ✅ Malware Detection: PASS" >> $VERIFICATION_LOG
else
    echo "❌ Malware Detection: FAIL"
    echo "[$(date)] ❌ Malware Detection: FAIL" >> $VERIFICATION_LOG
    ((ERRORS++))
fi

# 7. Payment System Verification
echo -e "\n💳 Payment System:"
verify_step "Payment Logging Test" "/home/<USER>/test-payment-logging.sh" "successful"
verify_step "PesaPal Configuration" "grep -q 'PESAPAL_ENVIRONMENT=live' /home/<USER>/jerryjoo-website/.env.production" ""

# 8. Monitoring and Logging
echo -e "\n📊 Monitoring & Logging:"
verify_step "System Monitor Script" "test -x /home/<USER>/enhanced-monitor.sh" ""
verify_step "Payment Analysis Script" "test -x /home/<USER>/payment-analysis.sh" ""
verify_step "Backup Script" "test -x /home/<USER>/enhanced-backup.sh" ""
verify_step "Log Rotation Config" "test -f /etc/logrotate.d/jerryjoo-payments" ""

# 9. Performance and Load Testing
echo -e "\n⚡ Performance Testing:"
# Basic load test
LOAD_TEST_RESULT=$(ab -n 50 -c 5 https://yourdomain.com/ 2>&1 | grep "Requests per second")
if [[ "$LOAD_TEST_RESULT" == *"Requests per second"* ]]; then
    echo "✅ Load Test: PASS - $LOAD_TEST_RESULT"
    echo "[$(date)] ✅ Load Test: PASS - $LOAD_TEST_RESULT" >> $VERIFICATION_LOG
else
    echo "❌ Load Test: FAIL"
    echo "[$(date)] ❌ Load Test: FAIL" >> $VERIFICATION_LOG
    ((ERRORS++))
fi

# 10. Backup System Verification
echo -e "\n💾 Backup System:"
# Test backup script
BACKUP_TEST_RESULT=$(/home/<USER>/enhanced-backup.sh 2>&1 | tail -1)
if [[ "$BACKUP_TEST_RESULT" == *"successful"* ]]; then
    echo "✅ Backup System: PASS"
    echo "[$(date)] ✅ Backup System: PASS" >> $VERIFICATION_LOG
else
    echo "❌ Backup System: FAIL"
    echo "[$(date)] ❌ Backup System: FAIL" >> $VERIFICATION_LOG
    ((ERRORS++))
fi

# 11. File Permissions Audit
echo -e "\n🔐 File Permissions Audit:"
verify_step "Environment File Permissions" "stat -c '%a' /home/<USER>/jerryjoo-website/.env.production" "644"
verify_step "Payment Logs Permissions" "stat -c '%a' /var/log/jerryjoo/payments" "750"
verify_step "Uploads Directory Permissions" "stat -c '%a' /home/<USER>/jerryjoo-website/uploads" "750"

# 12. Network Security Testing
echo -e "\n🌐 Network Security:"
# Test for open ports
OPEN_PORTS=$(nmap -sT localhost | grep "open" | wc -l)
if [ "$OPEN_PORTS" -le 5 ]; then  # Expect only essential ports
    echo "✅ Port Security: PASS ($OPEN_PORTS open ports)"
    echo "[$(date)] ✅ Port Security: PASS ($OPEN_PORTS open ports)" >> $VERIFICATION_LOG
else
    echo "⚠️ Port Security: WARNING ($OPEN_PORTS open ports - review needed)"
    echo "[$(date)] ⚠️ Port Security: WARNING ($OPEN_PORTS open ports)" >> $VERIFICATION_LOG
fi

# Final Results
echo -e "\n=================================================================="
echo "🎯 VERIFICATION SUMMARY"
echo "=================================================================="
echo "Total Errors: $ERRORS"
echo "Verification Log: $VERIFICATION_LOG"

if [ $ERRORS -eq 0 ]; then
    echo "🎉 ALL TESTS PASSED - PRODUCTION READY!"
    echo "[$(date)] 🎉 PRODUCTION VERIFICATION: ALL TESTS PASSED" >> $VERIFICATION_LOG
    exit 0
else
    echo "❌ $ERRORS TESTS FAILED - REVIEW REQUIRED"
    echo "[$(date)] ❌ PRODUCTION VERIFICATION: $ERRORS TESTS FAILED" >> $VERIFICATION_LOG
    echo "Please review the verification log and fix issues before going live."
    exit 1
fi
```

```bash
# Make verification script executable
chmod +x /home/<USER>/production-verification.sh

# Run comprehensive verification
./production-verification.sh
```

### **12.2 Post-Deployment Tasks**
1. **Change Default Credentials**
   - Update admin password in production
   - Generate new JWT secrets
   - Update PesaPal credentials

2. **Configure Monitoring**
   - Set up external monitoring (UptimeRobot, etc.)
   - Configure email alerts
   - Set up log aggregation

3. **Performance Optimization**
   - Enable Redis for session storage
   - Configure CDN for static assets
   - Optimize database indexes

4. **Security Audit**
   - Run security scan
   - Review firewall rules
   - Test backup/recovery procedures

---

## 🎉 **Enhanced Production Deployment Complete!**

Your Jerry Joo Music Website is now successfully deployed to production with a comprehensive hybrid storage architecture and enterprise-grade security:

### **🔐 Core Security Features**
- ✅ **Secure HTTPS with SSL certificates and auto-renewal**
- ✅ **Production-grade unified authentication system**
- ✅ **Enhanced XSS and CSRF protection with CSP headers**
- ✅ **Directory traversal protection and file type validation**
- ✅ **Anti-malware protection with ClamAV scanning**
- ✅ **Fail2ban intrusion prevention and IP blocking**
- ✅ **Advanced firewall configuration with UFW**

### **🗄️ Hybrid Storage Architecture**
- ✅ **Organized music file storage (singles/albums/previews by year)**
- ✅ **Secure upload processing with quarantine system**
- ✅ **Payment transaction logging with 90-day retention**
- ✅ **Automated file cleanup and malware scanning**
- ✅ **Artwork management with image-specific security**

### **💳 Payment System Enhancements**
- ✅ **Comprehensive JSON-based transaction logging**
- ✅ **Failed transaction tracking and analysis**
- ✅ **Daily payment summaries and reporting**
- ✅ **PesaPal webhook response logging**
- ✅ **Payment fraud detection and alerting**

### **📊 Advanced Monitoring & Alerting**
- ✅ **Real-time file system monitoring with inotify**
- ✅ **Payment transaction analysis and threshold alerting**
- ✅ **Disk space monitoring with automated cleanup**
- ✅ **Security incident detection and logging**
- ✅ **Performance monitoring with load testing**

### **💾 Enterprise Backup Strategy**
- ✅ **Multi-tier backup system (daily/weekly/monthly)**
- ✅ **Incremental music file backups**
- ✅ **Payment log archival with 90-day retention**
- ✅ **Backup integrity verification and testing**
- ✅ **RTO: 4 hours, RPO: 1 hour compliance**

### **🚨 Disaster Recovery Capabilities**
- ✅ **Comprehensive disaster recovery scripts**
- ✅ **Automated rollback point creation**
- ✅ **Granular recovery options (database/app/files/config)**
- ✅ **Full system recovery with verification**
- ✅ **Emergency procedures documentation**

### **🧪 Production Verification**
- ✅ **Comprehensive 50+ point verification checklist**
- ✅ **Automated security testing and validation**
- ✅ **Performance and load testing**
- ✅ **Authentication system verification**
- ✅ **Backup system testing and validation**

### **📈 Performance & Scalability**
- ✅ **PM2 clustering with auto-restart**
- ✅ **Nginx reverse proxy with rate limiting**
- ✅ **Gzip compression and caching**
- ✅ **MongoDB with authentication and optimization**
- ✅ **Log rotation and automated cleanup**

---

## 🌐 **Access Points**

**Main Website:** https://yourdomain.com
**Admin Panel:** https://yourdomain.com/myballs
**API Health Check:** https://yourdomain.com/api/health
**Music Files:** https://yourdomain.com/tracks/
**Artwork:** https://yourdomain.com/artwork/

---

## 🛠️ **Management Commands**

### **System Monitoring**
```bash
# Run comprehensive verification
./production-verification.sh

# Check system status
./enhanced-monitor.sh

# Analyze payment logs
./payment-analysis.sh
```

### **Backup & Recovery**
```bash
# Create manual backup
./enhanced-backup.sh

# List available backups
./disaster-recovery.sh list

# Create rollback point
./disaster-recovery.sh rollback-point before_update
```

### **Security Operations**
```bash
# Scan for malware
./scan-uploads.sh

# Check file system events
sudo journalctl -u jerryjoo-fs-monitor -f

# Review security logs
tail -f /var/log/jerryjoo/security/*.log
```

---

## � **Production Readiness Achieved**

Your Jerry Joo Music Website now features:

- **🔒 Enterprise-grade security** with multi-layered protection
- **📁 Hybrid storage architecture** optimized for music content
- **💳 Comprehensive payment logging** with fraud detection
- **📊 Advanced monitoring** with real-time alerting
- **💾 Robust backup strategy** with disaster recovery
- **🧪 Automated testing** and verification systems
- **⚡ High performance** with scalability features
- **🛡️ Proactive security** with malware protection

**🎵 Your music platform is enterprise-ready and built to scale!** 🎵

---

## 📞 **Support & Maintenance**

### **Daily Operations**
- Monitor system health via automated scripts
- Review security and payment logs
- Verify backup completion
- Check disk space and performance metrics

### **Weekly Tasks**
- Review security alerts and incidents
- Analyze payment transaction patterns
- Test backup restoration procedures
- Update system packages and dependencies

### **Monthly Tasks**
- Security audit and vulnerability assessment
- Performance optimization review
- Backup strategy evaluation
- Disaster recovery testing

**🚀 Your Jerry Joo Music Website is now live with enterprise-grade reliability!** 🚀
