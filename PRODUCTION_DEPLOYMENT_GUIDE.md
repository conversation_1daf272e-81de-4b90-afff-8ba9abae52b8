# 🚀 Jerry <PERSON> Music Website - Production Deployment Guide

**Target Platform:** Ubuntu Server 20.04/22.04 LTS  
**Deployment Type:** Full-stack web application with MongoDB  
**Security Level:** Production-grade with SSL/TLS

---

## 📋 **Prerequisites**

### **Server Requirements**
- **OS:** Ubuntu Server 20.04 LTS or newer
- **RAM:** Minimum 2GB (Recommended 4GB+)
- **Storage:** Minimum 20GB SSD (Recommended 50GB+)
- **CPU:** 2+ cores recommended
- **Network:** Public IP address with ports 80, 443, 22 open

### **Domain & DNS**
- Domain name pointing to server IP
- DNS A record configured
- Optional: Subdomain for admin panel

---

## 🔧 **Step 1: Server Setup & Security**

### **1.1 Initial Server Configuration**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Create application user
sudo adduser jerryjoo
sudo usermod -aG sudo jerryjoo

# Switch to application user
su - jerryjoo
```

### **1.2 SSH Security Hardening**
```bash
# Generate SSH key pair (on local machine)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to server
ssh-copy-id jerryjoo@your-server-ip

# Configure SSH (on server)
sudo nano /etc/ssh/sshd_config
```

**SSH Configuration:**
```
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AllowUsers jerryjoo
```

```bash
# Restart SSH service
sudo systemctl restart ssh
```

### **1.3 Firewall Configuration**
```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 2222/tcp  # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

---

## 📦 **Step 2: Install Dependencies**

### **2.1 Node.js Installation**
```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 9.x.x
```

### **2.2 MongoDB Installation**
```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start and enable MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify installation
sudo systemctl status mongod
```

### **2.3 Nginx Installation**
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

### **2.4 PM2 Process Manager**
```bash
# Install PM2 globally
sudo npm install -g pm2

# Configure PM2 to start on boot
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u jerryjoo --hp /home/<USER>
```

---

## 🗄️ **Step 3: Database Setup**

### **3.1 MongoDB Security Configuration**
```bash
# Connect to MongoDB
mongosh

# Create admin user
use admin
db.createUser({
  user: "admin",
  pwd: "STRONG_ADMIN_PASSWORD_HERE",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})

# Create application database and user
use jerryjoo_music
db.createUser({
  user: "jerryjoo_app",
  pwd: "STRONG_APP_PASSWORD_HERE",
  roles: ["readWrite"]
})

# Exit MongoDB shell
exit
```

### **3.2 Enable MongoDB Authentication**
```bash
# Edit MongoDB configuration
sudo nano /etc/mongod.conf
```

**Add to mongod.conf:**
```yaml
security:
  authorization: enabled

net:
  bindIp: 127.0.0.1
  port: 27017
```

```bash
# Restart MongoDB
sudo systemctl restart mongod
```

### **3.3 Database Backup Setup**
```bash
# Create backup directory
sudo mkdir -p /var/backups/mongodb
sudo chown jerryjoo:jerryjoo /var/backups/mongodb

# Create backup script
nano /home/<USER>/backup-db.sh
```

**Backup Script:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/mongodb"
DB_NAME="jerryjoo_music"

mongodump --host localhost:27017 --db $DB_NAME --username jerryjoo_app --password "STRONG_APP_PASSWORD_HERE" --out $BACKUP_DIR/backup_$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -type d -name "backup_*" -mtime +7 -exec rm -rf {} \;
```

```bash
# Make script executable
chmod +x /home/<USER>/backup-db.sh

# Add to crontab for daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup-db.sh
```

---

## 📁 **Step 4: Application Deployment**

### **4.1 Clone Repository**
```bash
# Navigate to home directory
cd /home/<USER>

# Clone the repository
git clone https://github.com/your-username/jerryjoo-website.git
cd jerryjoo-website

# Install dependencies
npm install
```

### **4.2 Environment Configuration**
```bash
# Create production environment file
nano .env.production
```

**Production Environment Variables:**
```env
# Server Configuration
NODE_ENV=production
PORT=5000
HOST=0.0.0.0

# Database Configuration
MONGODB_URI=******************************************************************************
MONGODB_DB_NAME=jerryjoo_music

# JWT Configuration
ADMIN_JWT_SECRET=GENERATE_STRONG_SECRET_HERE_64_CHARS_MIN
ADMIN_JWT_EXPIRES=8h

# File Storage
TRACKS_BASE_PATH=/home/<USER>/jerryjoo-website/TRACKS
UPLOADS_PATH=/home/<USER>/jerryjoo-website/uploads

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Payment Configuration
PESAPAL_CONSUMER_KEY=your-pesapal-consumer-key
PESAPAL_CONSUMER_SECRET=your-pesapal-consumer-secret
PESAPAL_ENVIRONMENT=live

# Security
CORS_ORIGIN=https://yourdomain.com
SESSION_SECRET=GENERATE_STRONG_SESSION_SECRET_HERE

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/jerryjoo/app.log
```

### **4.3 Create Required Directories**
```bash
# Create application directories
mkdir -p TRACKS uploads temp logs

# Create log directory
sudo mkdir -p /var/log/jerryjoo
sudo chown jerryjoo:jerryjoo /var/log/jerryjoo

# Set proper permissions
chmod 755 TRACKS uploads temp
chmod 644 .env.production
```

### **4.4 Build Frontend (if applicable)**
```bash
# Build React frontend for production
npm run build

# Verify build output
ls -la dist/
```

---

## 🔒 **Step 5: SSL Certificate Setup**

### **5.1 Install Certbot**
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx
```

### **5.2 Obtain SSL Certificate**
```bash
# Stop Nginx temporarily
sudo systemctl stop nginx

# Obtain certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Start Nginx
sudo systemctl start nginx
```

### **5.3 Configure Auto-renewal**
```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🌐 **Step 6: Nginx Configuration**

### **6.1 Create Nginx Configuration**
```bash
# Create site configuration
sudo nano /etc/nginx/sites-available/jerryjoo-music
```

**Nginx Configuration:**
```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;

# Upstream backend
upstream backend {
    server 127.0.0.1:5000;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Static file serving
    location /tracks/ {
        alias /home/<USER>/jerryjoo-website/TRACKS/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /uploads/ {
        alias /home/<USER>/jerryjoo-website/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Admin panel with rate limiting
    location /myballs {
        limit_req zone=admin burst=10 nodelay;
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API endpoints with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Frontend application
    location / {
        root /home/<USER>/jerryjoo-website/dist;
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
}
```

### **6.2 Enable Site Configuration**
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/jerryjoo-music /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

---

## 🚀 **Step 7: Process Management**

### **7.1 Create PM2 Ecosystem File**
```bash
# Create PM2 configuration
nano ecosystem.config.js
```

**PM2 Configuration:**
```javascript
module.exports = {
  apps: [{
    name: 'jerryjoo-backend',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/var/log/jerryjoo/error.log',
    out_file: '/var/log/jerryjoo/out.log',
    log_file: '/var/log/jerryjoo/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### **7.2 Start Application with PM2**
```bash
# Start application in production mode
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Check application status
pm2 status
pm2 logs jerryjoo-backend
```

### **7.3 PM2 Monitoring Setup**
```bash
# Install PM2 monitoring (optional)
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
pm2 set pm2-logrotate:compress true
```

---

## 🔐 **Step 8: Security Hardening**

### **8.1 Fail2Ban Installation**
```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Create custom configuration
sudo nano /etc/fail2ban/jail.local
```

**Fail2Ban Configuration:**
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
```

```bash
# Start and enable Fail2Ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

### **8.2 System Security Updates**
```bash
# Enable automatic security updates
sudo apt install -y unattended-upgrades

# Configure automatic updates
sudo nano /etc/apt/apt.conf.d/50unattended-upgrades
```

**Auto-update Configuration:**
```
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
```

### **8.3 File Permissions Security**
```bash
# Set secure file permissions
chmod 600 .env.production
chmod 755 src/
chmod 644 src/*.js
chmod 755 TRACKS/ uploads/

# Set ownership
sudo chown -R jerryjoo:jerryjoo /home/<USER>/jerryjoo-website
```

---

## 📊 **Step 9: Monitoring & Logging**

### **9.1 System Monitoring Setup**
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
nano /home/<USER>/monitor.sh
```

**Monitoring Script:**
```bash
#!/bin/bash
LOG_FILE="/var/log/jerryjoo/system-monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# System metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')

# Application status
PM2_STATUS=$(pm2 jlist | jq -r '.[0].pm2_env.status')

echo "[$DATE] CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}, App: ${PM2_STATUS}" >> $LOG_FILE
```

```bash
# Make script executable
chmod +x /home/<USER>/monitor.sh

# Add to crontab (every 5 minutes)
crontab -e
# Add: */5 * * * * /home/<USER>/monitor.sh
```

### **9.2 Application Logging Configuration**
```bash
# Create log rotation configuration
sudo nano /etc/logrotate.d/jerryjoo
```

**Log Rotation Configuration:**
```
/var/log/jerryjoo/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 jerryjoo jerryjoo
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## 🧪 **Step 10: Testing & Verification**

### **10.1 Health Check Script**
```bash
# Create health check script
nano /home/<USER>/health-check.sh
```

**Health Check Script:**
```bash
#!/bin/bash
DOMAIN="https://yourdomain.com"
ADMIN_PANEL="https://yourdomain.com/myballs"
API_HEALTH="https://yourdomain.com/api/health"

echo "🔍 Jerry Joo Music Website - Health Check"
echo "=========================================="

# Check main website
echo -n "Main Website: "
if curl -s -o /dev/null -w "%{http_code}" $DOMAIN | grep -q "200"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check admin panel
echo -n "Admin Panel: "
if curl -s -o /dev/null -w "%{http_code}" $ADMIN_PANEL | grep -q "200"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check API health
echo -n "API Health: "
if curl -s $API_HEALTH | grep -q "OK"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check database connection
echo -n "Database: "
if mongosh --quiet --eval "db.adminCommand('ping')" jerryjoo_music > /dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check PM2 status
echo -n "Application: "
if pm2 jlist | jq -r '.[0].pm2_env.status' | grep -q "online"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

echo "=========================================="
echo "Health check completed at $(date)"
```

```bash
# Make script executable
chmod +x /home/<USER>/health-check.sh

# Run health check
./health-check.sh
```

### **10.2 Performance Testing**
```bash
# Install Apache Bench for load testing
sudo apt install -y apache2-utils

# Test website performance
ab -n 100 -c 10 https://yourdomain.com/
ab -n 50 -c 5 https://yourdomain.com/api/health

# Test admin authentication
curl -X POST https://yourdomain.com/myballs/login \
  -H "Content-Type: application/json" \
  -d '{"username":"jerryjoo_admin","password":"JerryJoo2024!Admin"}'
```

---

## 🔄 **Step 11: Backup & Recovery**

### **11.1 Complete Backup Script**
```bash
# Create comprehensive backup script
nano /home/<USER>/full-backup.sh
```

**Full Backup Script:**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/jerryjoo"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/home/<USER>/jerryjoo-website"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
mongodump --host localhost:27017 --db jerryjoo_music \
  --username jerryjoo_app --password "STRONG_APP_PASSWORD_HERE" \
  --out $BACKUP_DIR/db_backup_$DATE

# Backup application files
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=logs \
  $APP_DIR

# Backup uploaded files
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz \
  $APP_DIR/TRACKS $APP_DIR/uploads

# Backup configuration
cp /etc/nginx/sites-available/jerryjoo-music $BACKUP_DIR/nginx_config_$DATE
cp $APP_DIR/.env.production $BACKUP_DIR/env_config_$DATE

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*backup*" -mtime +30 -delete

echo "Backup completed: $DATE"
```

```bash
# Make script executable
chmod +x /home/<USER>/full-backup.sh

# Add to crontab (daily at 3 AM)
crontab -e
# Add: 0 3 * * * /home/<USER>/full-backup.sh
```

### **11.2 Recovery Procedures**
```bash
# Create recovery documentation
nano /home/<USER>/RECOVERY_PROCEDURES.md
```

**Recovery Documentation:**
```markdown
# Recovery Procedures

## Database Recovery
1. Stop application: `pm2 stop jerryjoo-backend`
2. Restore database: `mongorestore --host localhost:27017 --db jerryjoo_music --username jerryjoo_app --password "PASSWORD" /path/to/backup`
3. Start application: `pm2 start jerryjoo-backend`

## Application Recovery
1. Extract backup: `tar -xzf app_backup_DATE.tar.gz`
2. Install dependencies: `npm install`
3. Restart with PM2: `pm2 restart jerryjoo-backend`

## Configuration Recovery
1. Restore Nginx config: `sudo cp nginx_config_DATE /etc/nginx/sites-available/jerryjoo-music`
2. Restore environment: `cp env_config_DATE .env.production`
3. Reload services: `sudo systemctl reload nginx && pm2 restart jerryjoo-backend`
```

---

## ✅ **Step 12: Final Verification Checklist**

### **12.1 Production Readiness Checklist**
```bash
# Run final verification
echo "🎵 Jerry Joo Music Website - Production Deployment Verification"
echo "=============================================================="

# Check all services
sudo systemctl status nginx mongod
pm2 status

# Verify SSL certificate
echo | openssl s_client -servername yourdomain.com -connect yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates

# Test authentication
curl -X POST https://yourdomain.com/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"jerryjoo_admin","password":"NEW_PRODUCTION_PASSWORD"}'

# Check file permissions
ls -la /home/<USER>/jerryjoo-website/.env.production

echo "=============================================================="
echo "✅ Production deployment verification completed!"
```

### **12.2 Post-Deployment Tasks**
1. **Change Default Credentials**
   - Update admin password in production
   - Generate new JWT secrets
   - Update PesaPal credentials

2. **Configure Monitoring**
   - Set up external monitoring (UptimeRobot, etc.)
   - Configure email alerts
   - Set up log aggregation

3. **Performance Optimization**
   - Enable Redis for session storage
   - Configure CDN for static assets
   - Optimize database indexes

4. **Security Audit**
   - Run security scan
   - Review firewall rules
   - Test backup/recovery procedures

---

## 🎉 **Deployment Complete!**

Your Jerry Joo Music Website is now successfully deployed to production with:

- ✅ **Secure HTTPS with SSL certificates**
- ✅ **Production-grade authentication system**
- ✅ **MongoDB database with backups**
- ✅ **Nginx reverse proxy with rate limiting**
- ✅ **PM2 process management**
- ✅ **Comprehensive monitoring and logging**
- ✅ **Automated security updates**
- ✅ **Backup and recovery procedures**

**Access your website at:** https://yourdomain.com
**Admin panel at:** https://yourdomain.com/myballs

🎵 **Your music website is live and ready for the world!** 🎵
