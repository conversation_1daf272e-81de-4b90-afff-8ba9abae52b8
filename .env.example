# Jerry Joo Music Website - Environment Configuration

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================
NODE_ENV=development
PORT=5000

# ============================================================================
# GOOGLE OAUTH CONFIGURATION
# ============================================================================
# Get this from Google Cloud Console (https://console.cloud.google.com/)
# Create OAuth 2.0 credentials and copy the client ID
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
MONGODB_URI=mongodb://localhost:27017/jerryjoo_music

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================
# Admin JWT Secret (change this in production!)
ADMIN_JWT_SECRET=admin_secret_key_jerry_joo_2024_change_in_production
ADMIN_JWT_EXPIRES=8h

# Session Secret (change this in production!)
SESSION_SECRET=jerry_joo_admin_session_secret_2024_change_in_production

# ============================================================================
# PAYMENT GATEWAYS
# ============================================================================

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_MODE=sandbox

# PesaPal Configuration (Kenya) - Production Ready
PESAPAL_CONSUMER_KEY=pSfoDU9I/jp6mKNLULgPHNwhjOgKSjlN
PESAPAL_CONSUMER_SECRET=N9aA/5nxH2Ge5WJ3Lvi6FpOUIAs=
PESAPAL_ENVIRONMENT=production

# PesaPal Sandbox Credentials (for testing)
# PESAPAL_CONSUMER_KEY=qkio1BGGYAXTu2JOfm7XSXNjRrK5NPlJ
# PESAPAL_CONSUMER_SECRET=osGQ364R49cXKeOYSpaOnT++rHs=
# PESAPAL_ENVIRONMENT=sandbox

# M-Pesa Configuration (Kenya)
MPESA_CONSUMER_KEY=your_mpesa_consumer_key_here
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret_here
MPESA_ENVIRONMENT=sandbox
MPESA_SHORTCODE=your_mpesa_shortcode_here
MPESA_PASSKEY=your_mpesa_passkey_here

# ============================================================================
# FILE STORAGE & CDN
# ============================================================================
# Base path for track storage
TRACKS_BASE_PATH=./TRACKS

# Cloudinary Configuration (optional)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AWS S3 Configuration (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=jerry-joo-music-files

# ============================================================================
# FRONTEND CONFIGURATION
# ============================================================================
FRONTEND_URL=http://localhost:8080
CALLBACK_BASE_URL=http://localhost:5000

# ============================================================================
# EMAIL CONFIGURATION
# ============================================================================
# SMTP Configuration for notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# SendGrid Configuration (alternative)
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=Jerry Joo Music

# ============================================================================
# ANALYTICS & MONITORING
# ============================================================================
# Google Analytics
GA_TRACKING_ID=UA-XXXXXXXXX-X

# Sentry Error Tracking
SENTRY_DSN=your_sentry_dsn_here

# ============================================================================
# SOCIAL MEDIA INTEGRATION
# ============================================================================
# YouTube API
YOUTUBE_API_KEY=your_youtube_api_key

# Spotify API
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret

# SoundCloud API
SOUNDCLOUD_CLIENT_ID=your_soundcloud_client_id
SOUNDCLOUD_CLIENT_SECRET=your_soundcloud_client_secret

# ============================================================================
# DEVELOPMENT SETTINGS
# ============================================================================
# Logging level (error, warn, info, http, debug)
LOG_LEVEL=debug

# Enable/disable certain features in development
ENABLE_RATE_LIMITING=false
ENABLE_CORS_STRICT=false
ENABLE_HTTPS_REDIRECT=false
